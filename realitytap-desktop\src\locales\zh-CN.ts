/**
 * 简体中文语言包
 */

export default {
  // === common ===
  common: {
    confirm: "确认",
    cancel: "取消",
    cancelled: "已取消操作",
    save: "保存",
    delete: "删除",
    close: "关闭",
    loading: "加载中...",
    error: "错误",
    clear: "清空",
    about: "关于",
    retry: "重试",
    dismiss: "忽略",
    enabled: "启用",
    disabled: "禁用"
  },
  // === librtcore ===
  librtcore: {
    error: {
      general: "librtcore 错误: {message}",
      retrying: "librtcore 初始化失败，正在重试: {message}",
      manualRetryRequired: "librtcore 初始化失败，需要手动重试: {message}",
      fallbackMode: "librtcore 进入降级模式，部分功能可能不可用",
      fallbackModeActive: "当前处于降级模式，触觉播放功能受限",
      fallbackModeExited: "已退出降级模式，功能恢复正常",
      resetting: "正在重置 librtcore 系统...",
      retrySuccess: "重试成功，librtcore 已恢复正常",
      retryFailed: "重试失败，请检查设备连接或配置",
      retryError: "重试过程中出现错误"
    }
  },
  // === app ===
  app: {
    title: "RealityTap Haptics Studio"
  },
  // === dashboard ===
  dashboard: {
    // === tabs ===
    tabs: {
      projects: "项目",
      learning: "学习资源"
    },
    // === projects ===
    projects: {
      newProject: "新建项目",
      newProjectSubtitle: "从音频或触觉文件创建",
      openProject: "打开项目",
      openProjectSubtitle: "从本地文件打开",
      recentProjects: "最近项目",
      exampleProjects: "示例项目",
      noRecentProjects: "暂无最近项目记录",
      goToLearning: "前往学习页面"
    }
  },
  // === project ===
  project: {
    // === create ===
    create: {
      success: "项目创建成功",
      failed: "创建项目失败"
    },
    // === open ===
    open: {
      failed: "打开项目失败",
      notFound: "项目不存在"
    },
    // === save ===
    save: {
      saving: "正在保存...",
      success: "文件保存成功",
      noData: "没有事件数据可以保存",
      noFileSelected: "没有选中的文件",
      saveFile: "保存文件 (Ctrl+S)",
      noChanges: "文件未修改或已保存"
    },
    // === undo ===
    undo: {
      undoAction: "撤销 (Ctrl+Z)",
      noUndoAvailable: "没有可撤销的操作",
      noFileSelected: "没有选中的文件"
    },
    // === redo ===
    redo: {
      redoAction: "重做 (Ctrl+Y)",
      noRedoAvailable: "没有可重做的操作",
      noFileSelected: "没有选中的文件"
    }
  },
  // === editor ===
  editor: {
    // === navigation ===
    navigation: {
      projects: "项目",
      doubleClickToEdit: "双击编辑",
      unsavedChanges: "有未保存的更改"
    },
    // === eventProperties ===
    eventProperties: {
      title: "事件属性",
      transientEvent: "瞬态事件",
      continuousEvent: "连续事件",
      startTime: "开始时间",
      intensity: "强度",
      frequency: "频率",
      duration: "持续时间",
      globalIntensity: "强度 (全局)",
      globalFrequency: "频率 (全局)",
      selectedCurvePoint: "已选曲线点",
      pointNumber: "点 #",
      time: "时间",
      pointRelativeIntensity: "点相对强度",
      pointRelativeFrequency: "点相对频率",
      computedAbsoluteIntensity: "计算绝对强度",
      computedAbsoluteFrequency: "计算绝对频率",
      firstLastPointZeroIntensity: "首尾点强度始终为零",
      frequencyAdjustmentHint: "提示: 按住{key}键并垂直拖动点以调整频率",
      selectEventToAdjust: "在时间线上选择一个事件以调整其属性。"
    },
    // === duration ===
    duration: {
      increased: "时长已增加 {duration} 毫秒"
    },
    // === durationPanel ===
    durationPanel: {
      increaseDuration: "增加时长",
      milliseconds: "毫秒",
      confirm: "确认",
      showAudio: "显示音频",
      hideAudio: "隐藏音频"
    },
    // === empty ===
    empty: {
      title: "添加新的触觉文件",
      description: "拖放单个音频文件或资源文件夹以开始设计您的触觉效果",
      addHapticFile: "添加触觉文件",
      addAudioFile: "添加音频文件",
      addVideoFile: "添加视频文件"
    },
    // === waveform ===
    waveform: {
      noFileSelected: "请先打开RealityTap Haptics波形描述文件后再编辑",
      loading: "正在加载RealityTap触觉效果..."
    },
    // === project ===
    project: {
      untitled: "未命名项目",
      renameSuccess: "项目名称已修改，目录已重命名",
      renameFailed: "项目重命名失败",
      noProjectLoaded: "当前没有加载项目。请创建或打开一个项目。"
    },
    // === file ===
    file: {
      confirmDelete: "确认删除",
      confirmDeleteMessage: "确定要删除文件 \"{name}\" 吗？此操作无法撤销。",
      createSuccess: "触觉文件创建成功",
      deleteSuccess: "文件 \"{name}\" 删除成功",
      deleteFailed: "删除文件 \"{name}\" 失败：{error}",
      selectFirst: "请先选择一个文件",
      noFileSelected: "没有选中的文件",
      noEventData: "没有事件数据可以保存",
      saveSuccess: "文件保存成功"
    },
    // === audio ===
    audio: {
      importingToRoot: "正在导入音频文件到根目录...",
      importingToGroup: "正在将音频文件导入到分组 \"{name}\"...",
      importSuccess: "音频导入并自动生成.he文件成功",
      importFailed: "音频导入失败：{error}",
      metadataFailed: "音频元数据解析失败，已导入但无法获取时长",
      amplitudeLoadFailed: "音频振幅数据加载失败：{error}",
      processingFailed: "音频处理失败：{error}",
      loadSuccess: "音频数据加载成功",
      loadFailed: "音频数据加载失败",
      noAudioFile: "当前文件没有关联的音频文件",
      checkFailed: "音频数据检查失败",
      waveformDisplay: "音频波形显示",
      noAudioData: "暂无音频数据"
    },
    // === video ===
    video: {
      importingToRoot: "正在导入视频文件到根目录...",
      importingToGroup: "正在将视频文件导入到分组 \"{name}\"...",
      importSuccess: "视频导入并自动生成.he文件成功",
      metadataFailed: "视频元数据解析失败，已导入但无法获取时长"
    },
    // === contextMenu ===
    contextMenu: {
      playEffect: "播放效果",
      renameFile: "重命名文件",
      deleteFile: "删除触觉文件",
      renameGroup: "重命名当前分组",
      deleteGroup: "删除当前分组",
      newHapticFile: "新建触觉文件",
      importAudioFile: "导入音频文件",
      importVideoFile: "导入视频文件",
      newRootGroup: "新建根分组",
      addFileToGroup: "添加文件到分组 \"{name}\"",
      importAudioToGroup: "导入音频到分组 \"{name}\"",
      importVideoToGroup: "导入视频到分组 \"{name}\"",
      newChildGroup: "在分组 \"{name}\" 下新建子分组",
      addEvent: "添加事件",
      transientEvent: "瞬态事件",
      continuousEvent: "连续事件",
      deleteEvent: "删除事件",
      needSpace: "需要至少 {space}ms 空间",
      availableSpace: "可用空间: {space}ms"
    },
    // === event ===
    event: {
      exceedsAudioDuration: "事件超出音频时长，无法添加/编辑！"
    },
    // === hapticFiles ===
    hapticFiles: {
      title: "触觉文件"
    },
    // === inlineEdit ===
    inlineEdit: {
      groupCreateSuccess: "分组 \"{name}\" 创建成功",
      groupCreateFailed: "创建分组失败：{error}",
      groupNameEmpty: "分组名称不能为空",
      groupNamePlaceholder: "输入分组名称",
      newGroupPlaceholder: "输入新分组名称",
      newFileNamePlaceholder: "输入新文件名",
      groupRenameSuccess: "分组已从 \"{oldName}\" 重命名为 \"{newName}\"",
      groupRenameFailed: "分组重命名失败：{error}",
      fileNameEmpty: "文件名不能为空",
      fileNameMustEndWithHe: "文件名必须以 .he 结尾",
      fileRenameSuccess: "文件已从 \"{oldName}\" 重命名为 \"{newName}\"",
      fileRenameFailed: "文件重命名失败：{error}"
    },
    // === groupDelete ===
    groupDelete: {
      confirmTitle: "确认删除分组",
      confirmMessage: "确定要删除分组 \"{name}\" 吗？",
      confirmWithContentTitle: "确认删除分组及其内容",
      confirmWithContentMessage: "分组 \"{name}\" 包含 {count} 个文件。确定要删除分组及其所有内容吗？",
      confirmDelete: "删除",
      deleteSuccess: "分组 \"{name}\" 删除成功",
      deleteWithContentSuccess: "分组 \"{name}\" 及其内容删除成功",
      deleteFailed: "删除分组失败：{error}",
      cancelled: "已取消删除操作"
    },
    // === dragAndDrop ===
    dragAndDrop: {
      targetFileNotFound: "目标文件未找到",
      cannotDropOnSelfOrDescendant: "不能将项目拖拽到自身或其子项上",
      fileMovedToGroup: "文件已移动到分组 \"{groupName}\"",
      fileMovedToRoot: "文件已移动到根目录",
      fileMoveSuccess: "文件移动成功",
      groupMovedToGroup: "分组已移动到分组 \"{groupName}\"",
      groupMovedToRoot: "分组已移动到根目录",
      groupMoveSuccess: "分组移动成功",
      moveFailed: "移动失败",
      moveToRootFailed: "移动到根目录失败",
      unknownGroup: "未知分组"
    }
  },
  // === device ===
  device: {
    // === status ===
    status: {
      connected: "已连接",
      disconnected: "未连接",
      connecting: "连接中",
      disconnecting: "断开中",
      error: "错误",
      unknown: "未知",
      noDevices: "无设备",
      title: "设备状态",
      total: "总设备",
      errorDevices: "错误设备",
      defaultDevice: "默认设备",
      clickToOpen: "点击打开设备管理器",
      default: "默认"
    },
    // === types ===
    types: {
      usb: "USB",
      wifi: "WiFi",
      bluetooth: "蓝牙"
    },
    // === actions ===
    actions: {
      scan: "扫描设备",
      refresh: "刷新",
      connect: "连接",
      disconnect: "断开连接",
      setDefault: "设为默认",
      rename: "重命名",
      remove: "移除",
      sendFile: "发送文件",
      addTestDevice: "添加测试设备",
      unsetDefault: "取消默认"
    },
    // === details ===
    details: {
      deviceId: "设备ID",
      deviceType: "设备类型",
      connectionStatus: "连接状态",
      lastConnected: "最后连接",
      manufacturer: "制造商",
      model: "型号"
    },
    // === rename ===
    rename: {
      title: "重命名设备",
      deviceName: "设备名称",
      placeholder: "请输入新的设备名称",
      nameRequired: "设备名称不能为空"
    },
    // === remove ===
    remove: {
      confirmTitle: "确认删除",
      confirmMessage: "确定要删除设备 \"{name}\" 吗？此操作无法撤销。"
    },
    // === transmission ===
    transmission: {
      heFile: "HE文件",
      audioFile: "音频文件",
      projectData: "项目数据",
      deviceConfig: "设备配置",
      priority: {
        low: "低",
        normal: "普通",
        high: "高",
        urgent: "紧急"
      }
    },
    // === testDevice ===
    testDevice: {
      name: "测试设备 {number}",
      manufacturer: "测试制造商",
      model: "测试型号"
    },
    // === errors ===
    errors: {
      unknownError: "未知错误",
      timeoutError: "连接超时",
      permissionDenied: "权限被拒绝",
      deviceNotFound: "设备未找到",
      deviceBusy: "设备忙碌",
      invalidParameter: "无效参数"
    },
    // === messages ===
    messages: {
      scanComplete: "设备扫描完成",
      scanFailed: "设备扫描失败",
      refreshSuccess: "设备列表已刷新",
      refreshFailed: "刷新失败",
      connectSuccess: "设备连接成功",
      connectFailed: "设备连接失败",
      disconnectSuccess: "设备断开成功",
      disconnectFailed: "设备断开失败",
      setDefaultSuccess: "默认设备设置成功",
      setDefaultFailed: "默认设备设置失败",
      removeSuccess: "设备删除成功",
      removeFailed: "设备删除失败",
      renameSuccess: "设备重命名成功",
      renameFailed: "设备重命名失败",
      sendFileInfo: "文件发送功能开发中...",
      sendFileFailed: "文件发送失败",
      addTestDeviceSuccess: "测试设备添加成功",
      addTestDeviceFailed: "添加测试设备失败",
      initializeFailed: "设备管理器初始化失败"
    },
    // === filter ===
    filter: {
      deviceType: "设备类型",
      connectionStatus: "连接状态",
      searchDevices: "搜索设备..."
    },
    // === management ===
    management: {
      title: "设备管理"
    }
  },

  // === errors ===
  errors: {
    unknown: "发生未知错误",
    networkError: "网络错误",
    fileNotFound: "文件未找到",
    operationFailed: "操作失败"
  },
  // === learning ===
  learning: {
    title: "触觉设计学习资源",
    // === gettingStarted ===
    gettingStarted: {
      title: "触觉设计入门",
      description: "学习触觉设计的基础知识以及如何创建有效的触觉反馈。",
      // === tags ===
      tags: {
        beginner: "初学者",
        tutorial: "教程"
      }
    },
    // === audioToHaptics ===
    audioToHaptics: {
      title: "音频到触觉转换",
      description: "将音频文件转换为有意义的触觉反馈的技巧和技术。",
      // === tags ===
      tags: {
        intermediate: "中级",
        tutorial: "教程"
      }
    },
    // === gaming ===
    gaming: {
      title: "游戏触觉设计",
      description: "在游戏应用中实现触觉反馈的最佳实践。",
      // === tags ===
      tags: {
        gaming: "游戏",
        caseStudy: "案例研究"
      }
    },
    // === uxDesign ===
    uxDesign: {
      title: "触觉UX设计",
      description: "如何在移动应用中使用触觉反馈来增强用户体验。",
      // === tags ===
      tags: {
        ux: "UX",
        mobile: "移动端"
      }
    },
    // === advanced ===
    advanced: {
      title: "高级触觉技术",
      description: "创建复杂和细致触觉体验的高级技术。",
      // === tags ===
      tags: {
        advanced: "高级",
        tutorial: "教程"
      }
    },
    // === research ===
    research: {
      title: "触觉反馈研究",
      description: "关于触觉反馈有效性的最新研究和学术研究。",
      // === tags ===
      tags: {
        research: "研究",
        academic: "学术"
      }
    }
  },
  // === examples ===
  examples: {
    // === populationOne ===
    populationOne: {
      haptics: "8个触觉效果"
    },
    notImplemented: "功能未实现",
    notImplementedMessage: "\"{title}\" 示例项目功能尚未实现，敬请期待！"
  },
  // === i18nTest ===
  i18nTest: {
    title: "国际化测试",
    languageSwitchTest: "语言切换测试",
    currentLanguage: "当前语言",
    commonTextTest: "通用文本测试",
    errorMessageTest: "错误消息测试",
    languageDetectionInfo: "语言检测信息",
    exampleError: "磁盘空间不足",
    // === labels ===
    labels: {
      confirm: "确认",
      cancel: "取消",
      save: "保存",
      delete: "删除",
      loading: "加载中",
      saveFailedExample: "保存失败 (示例)",
      unknownError: "未知错误",
      networkError: "网络错误",
      fileNotFound: "文件未找到"
    }
  },
  // === update ===
  update: {
    downloading: "下载中",
    installing: "安装中",
    updateAvailable: "有可用更新",
    newVersionAvailable: "发现新版本",
    downloadNow: "立即下载",
    installNow: "立即安装",
    remindLater: "稍后提醒",
    installConfirmTitle: "确认安装更新",
    installConfirmMessage: "安装更新需要关闭应用程序。安装完成后将自动重启到新版本。",
    installConfirmDetails: "整个过程大约需要 30 秒，请确保已保存所有工作。",
    installConfirmWarning: "安装过程中请勿手动关闭安装程序。",
    currentVersion: "当前版本",
    latestVersion: "最新版本",
    releaseNotes: "更新说明",
    fileSize: "文件大小",
    confirmInstallation: "确认安装",
    confirmInstall: "确认安装",
    installationNotice: "安装提示",
    applicationWillClose: "应用程序将关闭以完成安装，安装完成后会自动重新启动。",
    error: "更新错误",
    installingMessage: "正在安装更新，请稍候...",
    readyToInstall: "准备安装更新",
    downloadComplete: "下载完成",
    downloadCompleteMessage: "更新文件已下载完成，点击「立即安装」开始安装。",
    cancelling: "取消中",
    // === processManagement ===
    processManagement: {
      title: "进程管理确认",
      warningTitle: "警告",
      warningMessage: "安装更新需要关闭以下相关进程。请确保您已保存所有重要工作。",
      processListTitle: "需要关闭的进程",
      closeAndInstall: "关闭进程并安装",
      closeStrategyTitle: "关闭策略",
      gracefulClose: "优雅关闭",
      gracefulCloseDesc: "尝试正常关闭进程，给程序时间保存数据",
      forceClose: "强制关闭",
      forceCloseDesc: "立即终止进程，可能导致数据丢失",
      critical: "关键",
      noticeTitle: "重要提示",
      notice1: "请在关闭进程前保存所有重要工作",
      notice2: "关闭关键进程可能影响系统稳定性",
      notice3: "安装完成后应用程序将自动重启",
      // === processTypes ===
      processTypes: {
        MainApplication: "主应用",
        EditorWindow: "编辑器",
        RenderProcess: "渲染器",
        AudioService: "音频服务",
        FileMonitor: "文件监控",
        BackgroundService: "后台服务",
        ChildProcess: "子进程",
        Unknown: "未知进程"
      }
    }
  },
  // === forceUpdate ===
  forceUpdate: {
    title: "强制更新",
    notice: "重要更新",
    noticeMessage: "此更新为强制更新，必须安装后才能继续使用应用程序。",
    newVersionRequired: "需要更新到新版本",
    readyToInstall: "更新已下载完成，准备安装",
    startDownload: "开始下载",
    installNow: "立即安装",
    retryDownload: "重试下载",
    exitApplication: "关闭程序"
  },
  // === demo ===
  demo: {
    installProcessTitle: "安装流程",
    step1Title: "用户确认安装",
    step1Description: "显示安装确认对话框，说明应用将关闭并自动重启",
    step2Title: "创建安装脚本",
    step2Description: "生成独立的批处理脚本，包含安装和重启逻辑",
    step3Title: "启动独立安装",
    step3Description: "启动独立安装程序，当前应用退出",
    step4Title: "自动重启应用",
    step4Description: "安装完成后自动启动新版本应用"
  },
  // === about ===
  about: {
    title: "关于",
    loading: "正在加载版本信息...",
    version: "版本",
    buildInfo: "构建信息",
    platform: "平台",
    // === updateCheck ===
    updateCheck: {
      title: "版本检查",
      checking: "正在检查更新...",
      checkNow: "立即检查",
      newVersionAvailable: "发现新版本",
      latestVersion: "最新版本",
      upToDate: "当前版本已是最新",
      checkFailed: "检查更新失败",
      viewUpdate: "查看更新"
    }
  },
  // === installer ===
  installer: {
    ready: "准备就绪",
    preparing: "准备中",
    installing: "安装中",
    success: "安装成功",
    failed: "安装失败",
    cancelled: "已取消",
    completed: "已完成",
    // === operations ===
    operations: {
      initializing: "正在初始化",
      validating: "正在验证安装包",
      waitingForExit: "等待应用退出",
      backingUp: "正在备份文件",
      installing: "正在安装",
      restarting: "正在重启应用",
      completed: "安装完成",
      failed: "安装失败",
      cancelled: "已取消"
    },
    initializing: "正在初始化安装器",
    installSuccess: "安装成功完成",
    unknownError: "未知错误",
    preparingExit: "准备退出应用",
    cancelling: "正在取消",
    installCancelled: "安装已取消"
  },
  // === ota ===
  ota: {
    checkingUpdates: "正在检查更新",
    closingProcesses: "正在关闭进程",
    downloadFailed: "下载失败",
    downloadingUpdate: "正在下载更新",
    installationCompleted: "安装完成",
    installationFailed: "安装失败",
    installingUpdate: "正在安装更新",
    noDownloadedFile: "没有下载的文件",
    noUpdateInfo: "没有更新信息",
    noUpdatesAvailable: "没有可用更新",
    preparingInstallation: "准备安装",
    verificationFailed: "验证失败",
    verifyingUpdate: "正在验证更新"
  },
  // === debug ===
  debug: {
    info: "调试信息:",
    isLoading: "加载状态",
    hasRecentProjects: "有最近项目",
    recentProjectsLength: "最近项目数量",
    recentProjectsContent: "最近项目内容",
    title: "调试工具",
    activated: "调试模式已激活！",
    // === button ===
    button: {
      tooltip: "调试工具"
    },
    // === menu ===
    menu: {
      settings: "调试设置",
      viewLogs: "查看日志",
      openLogFolder: "打开日志文件夹",
      exportDebugInfo: "导出调试信息"
    },
    // === settings ===
    settings: {
      title: "调试设置",
      enableDebug: "启用调试模式",
      debugEnabled: "调试模式已启用",
      debugDisabled: "调试模式已禁用",
      debugEnabledDesc: "当前运行在开发模式下，调试功能已自动启用。",
      debugDisabledDesc: "当前运行在生产模式下，调试功能已自动禁用。",
      buildMode: "构建模式",
      currentConfig: "当前配置",
      logLevel: "日志级别",
      logOtaOperations: "记录OTA操作",
      logDeviceOperations: "记录设备操作",
      viewLogs: "查看日志",
      openLogFolder: "打开日志文件夹",
      resetDefault: "重置默认",
      saveSuccess: "调试设置保存成功",
      resetSuccess: "已重置为默认设置",
      // === notice ===
      notice: {
        title: "注意事项",
        sessionOnly: "调试设置仅在当前会话中生效，重启应用后恢复默认值",
        performance: "启用详细日志可能影响应用性能",
        logLocation: "日志文件保存在应用数据目录中"
      },
      // === validation ===
      validation: {
        levelRequired: "请选择日志级别"
      },
      // === errors ===
      errors: {
        loadFailed: "加载调试配置失败",
        saveFailed: "保存调试配置失败",
        openFolderFailed: "打开日志文件夹失败"
      }
    },
    // === logViewer ===
    logViewer: {
      title: "日志查看器",
      refresh: "刷新",
      clear: "清空",
      export: "导出",
      maxLines: "最大行数",
      autoRefreshOn: "自动刷新",
      autoRefreshOff: "手动刷新",
      loading: "正在加载日志...",
      noLogs: "暂无日志内容",
      logPath: "日志路径",
      unknown: "未知",
      lines: "行数",
      size: "大小",
      clearSuccess: "日志已清空",
      exportSuccess: "调试信息已导出到: {path}",
      // === errors ===
      errors: {
        getPathFailed: "获取日志文件路径失败",
        readFailed: "读取日志文件失败",
        clearFailed: "清空日志文件失败",
        exportFailed: "导出调试信息失败"
      }
    },
    // === errors ===
    errors: {
      openFolderFailed: "打开日志文件夹失败",
      exportFailed: "导出调试信息失败"
    },
    exportSuccess: "调试信息已导出到: {path}"
  },
  // === playEffect ===
  playEffect: {
    loading: "正在加载文件数据...",
    dialogTitle: "播放效果 - {fileName}",
    dialogTitleDefault: "播放效果",
    fileInfo: "文件信息",
    fileName: "文件名",
    fileUuid: "文件UUID",
    filePath: "文件路径",
    lastModified: "最后修改",
    jsonData: "JSON数据",
    copyJson: "复制JSON",
    downloadJson: "下载JSON",
    copySuccess: "JSON数据已复制到剪贴板",
    copyFailed: "复制失败",
    downloadSuccess: "JSON文件下载成功",
    downloadFailed: "下载失败",
    errorPlaying: "播放效果失败: {error}",
    notImplemented: "播放效果功能未实现",

    // 新增的播放控制相关
    play: "播放",
    stop: "停止",
    pause: "暂停",
    resume: "继续",
    save: "保存",
    saveAsRtp: "保存为RTP",
    saveToFile: "保存到文件",
    saving: "正在保存...",
    saveSuccess: "保存成功",
    saveFailed: "保存失败: {error}",
    selectSaveLocation: "选择保存位置",
    saveDialogTitle: "保存触觉数据文件",
    noData: "没有数据可保存",

    // 马达选择相关
    motorSelection: "马达选择",
    motorModel: "马达型号",
    selectMotor: "请选择马达型号",
    motorLoadFailed: "加载马达配置失败，使用默认配置",

    // 采样率选择相关
    samplingRateSelection: "采样率选择",
    selectSamplingRate: "请选择采样率",
    samplingRate6Khz: "6KHz - 基础效果",
    samplingRate8Khz: "8KHz - 通用选择",
    samplingRate12Khz: "12KHz - 中等精度",
    samplingRate24Khz: "24KHz - 高精度效果",

    // 实际频率选择相关
    actualFrequencySelection: "实际频率选择",
    selectActualFrequency: "请选择实际频率",

    // Canvas 相关
    canvas: "画布",
    noEvents: "暂无事件数据",
    timeAxis: "时间轴",
    amplitudeAxis: "强度轴",



    // 控制面板
    controls: "播放控制",
    settings: "设置",
    volume: "音量",
    loop: "循环播放",

    // librtcore 集成相关
    initializingLibrtcore: "正在初始化触觉算法库...",
    initSuccess: "触觉算法库初始化成功",
    reinitSuccess: "触觉算法库重新初始化成功",
    librtcoreError: "触觉算法库错误",
    retryInit: "重试初始化",
    clearError: "清除错误"
  }
} as const;
