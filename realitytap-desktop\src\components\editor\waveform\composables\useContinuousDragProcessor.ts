import type { RenderableContinuousEvent, RenderableEvent } from "@/types/haptic-editor";
import type { DragTarget } from "./useDragState";
import type { DragHandlerConfig } from "./useDragConfig";

import { MAX_FREQUENCY, MIN_FREQUENCY } from "../utils/drawing-helpers";
import { useDragBoundaryValidator } from "./useDragBoundaryValidator";
import { useDragHelpers } from "./useDragHelpers";
import { logger, LogModule } from "@/utils/logger/logger";
import type { Ref } from "vue";

/**
 * 连续事件拖拽处理器
 * 专门处理连续事件的复杂拖拽逻辑，包括曲线点的各种拖拽操作
 */
export function useContinuousDragProcessor(
  config: DragHandlerConfig,
  // 拖拽状态
  draggedEvent: Ref<RenderableEvent | null>,
  draggedCurveIndex: Ref<number>,
  dragStartX: Ref<number>,
  dragStartY: Ref<number>,
  currentDraggedTimeOffset: Ref<number>,
  // 状态更新函数
  setupCurvePointDrag: (event: RenderableEvent, continuousEvent: RenderableContinuousEvent, curveIndex: number) => void,
  setupEventDrag: (event: RenderableEvent) => void,
  updateEventDragValues: (timeOffset: number) => void,
  updateCurvePointFrequencyDragValues: (relativeFreq: number, absoluteFreq: number) => void,
  updateCurvePointPositionDragValues: (timeOffset: number, intensity: number, rawIntensity: number) => void,
  // 辅助函数
  isFrequencyAdjustmentKeyPressed: () => boolean,
  getEvents: () => RenderableEvent[]
) {
  const boundaryValidator = useDragBoundaryValidator();
  const {
    validateFirstCurvePointHorizontalDrag,
    validateLastCurvePointHorizontalDrag,
    validateCurvePointFrequencyBoundary,
    validateCurvePointPositionBoundary,
  } = boundaryValidator;

  const dragHelpers = useDragHelpers();
  const {
    calculateFrequencyChange,
    calculateRawIntensityRatio,
    validateIntensity,
    handleIntensityWithAutoGlobalAdjustment,
  } = dragHelpers;

  /**
   * 检测连续事件的命中测试
   * @param clickX 点击的X坐标
   * @param clickY 点击的Y坐标
   * @param event 连续事件
   * @param curvePointIndex 命中的曲线点索引（-1表示未命中曲线点）
   * @returns 命中测试结果
   */
  const hitTestContinuousEvent = (
    clickX: number,
    clickY: number,
    event: RenderableContinuousEvent,
    curvePointIndex: number
  ): { hitType: "curvePoint" | "body" | "none"; curveIndex: number } => {
    if (curvePointIndex >= 0) {
      return { hitType: "curvePoint", curveIndex: curvePointIndex };
    }

    // 检查是否命中事件体
    const eventStartX = config.mapTimeToXLocal(event.startTime);
    const eventEndX = config.mapTimeToXLocal(event.stopTime);
    const eventTopY = config.mapIntensityToYLocal(event.eventIntensity);
    const eventBottomY = config.mapIntensityToYLocal(0);

    if (clickX >= eventStartX && clickX <= eventEndX && clickY >= eventTopY && clickY <= eventBottomY) {
      return { hitType: "body", curveIndex: -1 };
    }

    return { hitType: "none", curveIndex: -1 };
  };

  /**
   * 处理连续事件的鼠标按下事件
   * @param clickX 点击的X坐标
   * @param clickY 点击的Y坐标
   * @param event 连续事件
   * @param curvePointIndex 命中的曲线点索引
   * @returns 是否处理了该事件
   */
  const handleContinuousMouseDown = (
    clickX: number,
    clickY: number,
    event: RenderableContinuousEvent,
    curvePointIndex: number
  ): { handled: boolean; targetType?: DragTarget; curveIndex?: number } => {
    const hitResult = hitTestContinuousEvent(clickX, clickY, event, curvePointIndex);

    if (hitResult.hitType === "curvePoint") {
      // 命中连续事件曲线点
      setupCurvePointDrag(event, event, hitResult.curveIndex);
      return {
        handled: true,
        targetType: "continuousCurvePoint",
        curveIndex: hitResult.curveIndex,
      };
    } else if (hitResult.hitType === "body") {
      // 命中连续事件体
      setupEventDrag(event);
      return { handled: true, targetType: "event" };
    }

    return { handled: false };
  };

  /**
   * 处理第一个Curve点的水平拖拽（调整事件startTime，保持endTime不变）
   */
  const handleFirstCurvePointHorizontalDrag = (currentX: number, continuousEvent: RenderableContinuousEvent) => {
    // 计算水平拖拽的时间偏移
    const deltaX = currentX - dragStartX.value;
    const timeOffsetChange = config.mapXOffsetToTimeOffsetLocal(deltaX);

    // 计算新的startTime
    let newStartTime = continuousEvent.startTime + timeOffsetChange;

    // 使用边界验证器进行验证
    const validation = validateFirstCurvePointHorizontalDrag(continuousEvent, newStartTime, getEvents());

    if (!validation.isValid) {
      // 验证失败，不允许拖拽
      return;
    }

    newStartTime = validation.adjustedValue || newStartTime;

    // 更新拖拽值（用于预览显示）
    const timeOffset = newStartTime - continuousEvent.startTime;
    updateEventDragValues(timeOffset);

    // 同时更新duration相关的拖拽状态
    currentDraggedTimeOffset.value = timeOffset;
  };

  /**
   * 处理最后一个Curve点的水平拖拽（调整事件endTime，保持startTime不变）
   */
  const handleLastCurvePointHorizontalDrag = (currentX: number, continuousEvent: RenderableContinuousEvent) => {
    // 计算水平拖拽的时间偏移
    const deltaX = currentX - dragStartX.value;
    const timeOffsetChange = config.mapXOffsetToTimeOffsetLocal(deltaX);

    // 计算新的endTime
    let newEndTime = continuousEvent.stopTime + timeOffsetChange;

    // 使用边界验证器进行验证
    const validation = validateLastCurvePointHorizontalDrag(continuousEvent, newEndTime, getEvents());

    if (!validation.isValid) {
      // 验证失败，不允许拖拽
      return;
    }

    newEndTime = validation.adjustedValue || newEndTime;

    // 更新拖拽值（用于预览显示）
    // 对于最后一个点的拖拽，我们需要更新duration而不是startTime偏移
    const newDuration = newEndTime - continuousEvent.startTime;
    const durationChange = newDuration - continuousEvent.duration;
    updateEventDragValues(0); // startTime不变

    // 更新duration相关的拖拽状态
    currentDraggedTimeOffset.value = durationChange; // 用于传递duration变化信息
  };

  /**
   * 处理曲线点频率调整的拖拽
   */
  const handleCurvePointFrequencyDrag = (currentY: number, continuousEvent: RenderableContinuousEvent, currentIndex: number) => {
    // 频率调节键按下时，垂直拖动调整相对频率
    const deltaY = dragStartY.value - currentY;
    const frequencyChange = calculateFrequencyChange(deltaY);
    let newRelativeFrequency = continuousEvent.curves[currentIndex].relativeCurveFrequency + frequencyChange;

    // 使用边界验证器验证频率
    const validation = validateCurvePointFrequencyBoundary(newRelativeFrequency, continuousEvent.eventFrequency, MIN_FREQUENCY, MAX_FREQUENCY);

    if (validation.isValid) {
      const validatedRelativeFreq = validation.adjustedValue || newRelativeFrequency;
      const newAbsoluteFrequency = validatedRelativeFreq + continuousEvent.eventFrequency;

      updateCurvePointFrequencyDragValues(validatedRelativeFreq, newAbsoluteFrequency);
    }
  };

  /**
   * 处理中间曲线点位置和强度的拖拽
   */
  const handleCurvePointPositionDrag = (currentX: number, currentY: number, continuousEvent: RenderableContinuousEvent, currentIndex: number) => {
    const curves = continuousEvent.curves;

    // 中间点：可以调整位置和强度
    const yIntensity = config.mapYToIntensityLocal(currentY);

    // 计算新的时间偏移
    const deltaX = currentX - dragStartX.value;
    const timeOffsetChange = config.mapXToTimeOffsetLocal(deltaX);
    const newTimeOffset = curves[currentIndex].timeOffset + timeOffsetChange;

    // 使用边界验证器验证位置（但不验证强度，因为我们要智能调整）
    const timeValidation = validateCurvePointPositionBoundary(continuousEvent, currentIndex, newTimeOffset, 0);

    if (timeValidation.isValid) {
      const clampedTimeOffset = timeValidation.adjustedValue || newTimeOffset;

      // 使用智能强度调整：如果目标强度超过全局强度，自动提升全局强度
      const intensityAdjustment = handleIntensityWithAutoGlobalAdjustment(
        yIntensity,
        continuousEvent.eventIntensity,
        {
          buffer: 0.05, // 5% 缓冲
          maxIntensity: 100,
          enableAutoAdjust: true
        }
      );

      // 如果需要更新全局强度，先更新事件的全局强度
      if (intensityAdjustment.needsGlobalUpdate) {
        logger.debug(LogModule.WAVEFORM, "🎯 自动调整全局强度", {
          originalGlobalIntensity: continuousEvent.eventIntensity,
          targetCurveIntensity: intensityAdjustment.originalTargetIntensity,
          newGlobalIntensity: intensityAdjustment.newGlobalIntensity,
          curveIndex: currentIndex
        });

        // 更新事件的全局强度
        const globalUpdatePayload = {
          Type: "continuous",
          RelativeTime: continuousEvent.startTime,
          Duration: continuousEvent.duration,
          Parameters: {
            Intensity: intensityAdjustment.newGlobalIntensity,
            Frequency: continuousEvent.eventFrequency,
          },
        };

        // 通过Store更新全局强度
        config.waveformStore.updateSelectedEvent(globalUpdatePayload);

        // 更新本地事件对象的强度值，确保后续计算使用新值
        continuousEvent.eventIntensity = intensityAdjustment.newGlobalIntensity;
      }

      // 使用调整后的强度值
      const finalCurveIntensity = intensityAdjustment.adjustedCurveIntensity;

      // 更新位置和强度拖拽值
      const rawIntensity = calculateRawIntensityRatio(finalCurveIntensity, intensityAdjustment.newGlobalIntensity);
      updateCurvePointPositionDragValues(clampedTimeOffset, finalCurveIntensity, rawIntensity);
    }
  };

  /**
   * 处理连续事件曲线点的拖拽
   */
  const handleCurvePointDrag = (currentX: number, currentY: number) => {
    if (draggedCurveIndex.value < 0 || !draggedEvent.value || draggedEvent.value.type !== "continuous") return;

    const continuousEvent = draggedEvent.value as RenderableContinuousEvent;
    const currentIndex = draggedCurveIndex.value;
    const curves = continuousEvent.curves;

    if (isFrequencyAdjustmentKeyPressed()) {
      // 频率调节键按下时，垂直拖动调整相对频率
      handleCurvePointFrequencyDrag(currentY, continuousEvent, currentIndex);
    } else {
      // 特殊处理：第一个Curve点的水平拖拽（调整事件startTime）
      if (currentIndex === 0) {
        handleFirstCurvePointHorizontalDrag(currentX, continuousEvent);
        return;
      }

      // 特殊处理：最后一个Curve点的水平拖拽（调整事件endTime）
      if (currentIndex === curves.length - 1) {
        handleLastCurvePointHorizontalDrag(currentX, continuousEvent);
        return;
      }

      // 中间点：可以调整位置和强度
      handleCurvePointPositionDrag(currentX, currentY, continuousEvent, currentIndex);
    }
  };

  /**
   * 设置连续事件拖拽的光标样式
   */
  const setContinuousDragCursor = (targetType: DragTarget, curveIndex?: number) => {
    if (!config.canvas.value) return;

    if (targetType === "continuousCurvePoint" && curveIndex !== undefined) {
      if (isFrequencyAdjustmentKeyPressed()) {
        config.canvas.value.style.cursor = "ns-resize";
      } else {
        const continuousEvent = draggedEvent.value as RenderableContinuousEvent;
        if (curveIndex === 0) {
          // 第一个Curve点：水平拖拽调整startTime
          config.canvas.value.style.cursor = "ew-resize";
        } else if (curveIndex === continuousEvent.curves.length - 1) {
          // 最后一个Curve点：水平拖拽调整endTime
          config.canvas.value.style.cursor = "ew-resize";
        } else {
          // 中间Curve点：可以移动
          config.canvas.value.style.cursor = "move";
        }
      }
    } else if (targetType === "event") {
      config.canvas.value.style.cursor = "grabbing";
    }
  };

  return {
    // 命中测试
    hitTestContinuousEvent,

    // 事件处理
    handleContinuousMouseDown,
    handleCurvePointDrag,

    // 特殊拖拽处理
    handleFirstCurvePointHorizontalDrag,
    handleLastCurvePointHorizontalDrag,
    handleCurvePointFrequencyDrag,
    handleCurvePointPositionDrag,

    // UI 相关
    setContinuousDragCursor,
  };
}
