import type { RenderableEvent, RenderableContinuousEvent, RenderableContinuousCurvePoint, RenderableTransientEvent } from "@/types/haptic-editor";
import type { SelectedEvent } from "@/components/editor/adjustments/HapticEventPropertiesPanel.vue";

export const DEFAULT_DURATION_MS = 30; // Default base duration if no content

// 文件级别的编辑器状态接口
export interface FileEditorState {
  selectedEventId: string | null;
  selectedCurvePointIndex: number;
  events: RenderableEvent[];
  totalDuration: number;
  isDurationLockedByAudio: boolean;
  isAdjustingProperties: boolean;
  // UI 状态 - 扩展为完整的Chrome标签页级别状态
  scrollPosition: number;
  zoomLevel: number;
  viewportStart: number;
  viewportEnd: number;
  // 新增：完整的UI交互状态
  canvasScrollLeft: number;
  canvasScrollTop: number;
  waveformContainerWidth: number;
  waveformContainerHeight: number;
  // 新增：编辑历史状态
  undoStack: RenderableEvent[][];
  redoStack: RenderableEvent[][];
  // 新增：文件特定的显示设置
  audioAmplitudeData: any | null;
  audioDuration: number | null;
  // 新增：最后活跃时间（用于LRU缓存策略）
  lastActiveTime: number;
}

// 定义创建事件的配置接口
export interface CreateEventConfig {
  type: "transient" | "continuous";
  startTime: number;
  intensity: number;
  frequency: number;
  duration?: number;
  availableSpace?: number;
}

// 定义批量更新选项接口
export interface BatchUpdateOptions {
  preserveSelection?: boolean; // 是否保持当前选中状态，默认 true
  skipValidation?: boolean; // 是否跳过数据验证，默认 false
  forceRedraw?: boolean; // 是否强制重绘，默认 true
  skipFileStateSync?: boolean; // 是否跳过文件状态同步，默认 false
  skipHistoryRecord?: boolean; // 是否跳过历史记录，默认 false
  isFileInitialLoad?: boolean; // 是否为文件初次加载，默认 false
  source?: 'user_action' | 'file_load' | 'cache_restore' | 'auto_sync'; // 数据来源，用于优化历史记录处理
}

// 定义事件更新接口
export interface EventUpdateItem {
  id: string;
  data: Partial<RenderableEvent>;
}

// 定义数据验证结果接口
export interface ValidationResult {
  valid: boolean;
  errors: string[];
}

// 将RenderableEvent转换为SelectedEvent的辅助函数
export const convertToSelectedEvent = (event: RenderableEvent | null): SelectedEvent | null => {
  if (!event) return null;

  if (event.type === "transient") {
    return {
      Type: "transient",
      RelativeTime: event.startTime,
      Parameters: {
        Intensity: event.intensity,
        Frequency: event.frequency,
      },
    };
  } else if (event.type === "continuous") {
    return {
      Type: "continuous",
      RelativeTime: event.startTime,
      Duration: event.duration, // 使用event.duration而不是计算值
      Parameters: {
        Intensity: event.eventIntensity,
        Frequency: event.eventFrequency,
      },
    };
  }

  return null;
};

// 导出类型
export type { RenderableEvent, RenderableContinuousEvent, RenderableContinuousCurvePoint, RenderableTransientEvent, SelectedEvent };
