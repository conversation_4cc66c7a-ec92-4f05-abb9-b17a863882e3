import type { RenderableContinuousEvent, RenderableEvent } from "@/types/haptic-editor";
import { MIN_CURVE_POINT_INTERVAL_MS, MIN_EVENT_INTERVAL_MS } from "../config/waveform-constants";
import { getEventEffectiveDuration } from "../utils/event-space";

// 拖拽辅助函数类型定义
export interface DragValidationResult {
  isValid: boolean;
  adjustedValue?: number;
  errorMessage?: string;
}

export interface CollisionCheckResult {
  hasCollision: boolean;
  adjustedStartTime?: number;
  collidingEvent?: RenderableEvent;
}

export interface BoundaryCheckResult {
  isWithinBounds: boolean;
  adjustedValue?: number;
  boundaryType?: 'min' | 'max' | 'duration';
  needsTimeExtension?: boolean;
  suggestedTotalDuration?: number;
}

// 拖拽辅助函数集合
export function useDragHelpers() {

  // 检查事件时间边界
  const checkTimeBoundary = (
    startTime: number,
    eventDuration: number,
    totalDuration: number,
    isDurationLockedByAudio: boolean = false
  ): BoundaryCheckResult => {
    // 检查最小边界
    if (startTime < 0) {
      return {
        isWithinBounds: false,
        adjustedValue: 0,
        boundaryType: 'min'
      };
    }

    // 检查最大边界
    const endTime = startTime + eventDuration;
    if (endTime > totalDuration) {
      // 如果是动态时长模式（没有音频锁定），允许超出当前总时长
      if (!isDurationLockedByAudio) {
        return {
          isWithinBounds: true,
          adjustedValue: startTime,
          needsTimeExtension: true,
          suggestedTotalDuration: endTime
        };
      }

      // 音频锁定模式下，不允许超出总时长
      return {
        isWithinBounds: false,
        adjustedValue: totalDuration - eventDuration,
        boundaryType: 'duration'
      };
    }

    return {
      isWithinBounds: true,
      adjustedValue: startTime
    };
  };

  // 检查事件碰撞
  const checkEventCollision = (
    draggedEvent: RenderableEvent,
    newStartTime: number,
    allEvents: RenderableEvent[]
  ): CollisionCheckResult => {
    const eventDuration = getEventEffectiveDuration(draggedEvent);

    // 找到当前事件在列表中的位置
    const currentIndex = allEvents.findIndex(e => e.id === draggedEvent.id);
    if (currentIndex === -1) {
      return { hasCollision: false };
    }

    let adjustedStartTime = newStartTime;
    let hasCollision = false;
    let collidingEvent: RenderableEvent | undefined;

    // 检查与前一个事件的碰撞（包含最小间隔要求）
    if (currentIndex > 0) {
      const prevEvent = allEvents[currentIndex - 1];
      const minAllowedStartTime = prevEvent.stopTime + MIN_EVENT_INTERVAL_MS;
      if (newStartTime < minAllowedStartTime) {
        hasCollision = true;
        adjustedStartTime = minAllowedStartTime;
        collidingEvent = prevEvent;
      }
    }

    // 检查与后一个事件的碰撞（包含最小间隔要求）
    if (currentIndex < allEvents.length - 1) {
      const nextEvent = allEvents[currentIndex + 1];
      const finalEndTime = adjustedStartTime + eventDuration;
      const maxAllowedEndTime = nextEvent.startTime - MIN_EVENT_INTERVAL_MS;
      if (finalEndTime > maxAllowedEndTime) {
        hasCollision = true;
        // 计算调整后的开始时间，确保不会产生负数
        // 修复问题：在极端情况下（如事件持续时间很长或下一个事件很近），
        // maxAllowedEndTime - eventDuration 可能产生负数，需要额外保护
        const calculatedStartTime = maxAllowedEndTime - eventDuration;
        adjustedStartTime = Math.max(0, calculatedStartTime);
        collidingEvent = nextEvent;
      }
    }

    return {
      hasCollision,
      adjustedStartTime: Math.max(0, adjustedStartTime),
      collidingEvent
    };
  };

  // 验证强度值
  const validateIntensity = (intensity: number, maxIntensity: number = 100): DragValidationResult => {
    if (intensity < 0) {
      return {
        isValid: false,
        adjustedValue: 0,
        errorMessage: '强度不能小于0'
      };
    }

    if (intensity > maxIntensity) {
      return {
        isValid: false,
        adjustedValue: maxIntensity,
        errorMessage: `强度不能大于${maxIntensity}`
      };
    }

    return {
      isValid: true,
      adjustedValue: intensity
    };
  };

  // 验证频率值
  const validateFrequency = (
    frequency: number,
    minFreq: number = 20,
    maxFreq: number = 1000
  ): DragValidationResult => {
    if (frequency < minFreq) {
      return {
        isValid: false,
        adjustedValue: minFreq,
        errorMessage: `频率不能小于${minFreq}Hz`
      };
    }

    if (frequency > maxFreq) {
      return {
        isValid: false,
        adjustedValue: maxFreq,
        errorMessage: `频率不能大于${maxFreq}Hz`
      };
    }

    return {
      isValid: true,
      adjustedValue: frequency
    };
  };

  // 验证相对频率值
  const validateRelativeFrequency = (relativeFreq: number): DragValidationResult => {
    if (relativeFreq < -100) {
      return {
        isValid: false,
        adjustedValue: -100,
        errorMessage: '相对频率不能小于-100'
      };
    }

    if (relativeFreq > 100) {
      return {
        isValid: false,
        adjustedValue: 100,
        errorMessage: '相对频率不能大于100'
      };
    }

    return {
      isValid: true,
      adjustedValue: relativeFreq
    };
  };

  // 验证时间偏移（用于曲线点）
  const validateTimeOffset = (
    timeOffset: number,
    minOffset: number,
    maxOffset: number
  ): DragValidationResult => {
    if (timeOffset < minOffset) {
      return {
        isValid: false,
        adjustedValue: minOffset,
        errorMessage: `时间偏移不能小于${minOffset}`
      };
    }

    if (timeOffset > maxOffset) {
      return {
        isValid: false,
        adjustedValue: maxOffset,
        errorMessage: `时间偏移不能大于${maxOffset}`
      };
    }

    return {
      isValid: true,
      adjustedValue: timeOffset
    };
  };

  // 计算曲线点的时间偏移边界
  const calculateCurvePointBounds = (
    continuousEvent: RenderableContinuousEvent,
    curveIndex: number
  ): { minTimeOffset: number; maxTimeOffset: number } => {
    const curves = continuousEvent.curves;

    // 首点和尾点的边界
    if (curveIndex === 0) {
      return { minTimeOffset: 0, maxTimeOffset: 0 };
    }

    if (curveIndex === curves.length - 1) {
      return {
        minTimeOffset: continuousEvent.duration,
        maxTimeOffset: continuousEvent.duration
      };
    }

    // 中间点的边界由相邻点决定
    const minTimeOffset = curves[curveIndex - 1].timeOffset + MIN_CURVE_POINT_INTERVAL_MS;
    const maxTimeOffset = curves[curveIndex + 1].timeOffset - MIN_CURVE_POINT_INTERVAL_MS;

    return { minTimeOffset, maxTimeOffset };
  };

  // 计算拖拽距离
  const calculateDragDistance = (
    startX: number,
    startY: number,
    currentX: number,
    currentY: number
  ): { distance: number; deltaX: number; deltaY: number } => {
    const deltaX = currentX - startX;
    const deltaY = currentY - startY;
    const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

    return { distance, deltaX, deltaY };
  };

  // 检查是否应该开始拖拽（基于距离阈值）
  const shouldStartDrag = (
    startX: number,
    startY: number,
    currentX: number,
    currentY: number,
    threshold: number = 5
  ): boolean => {
    const { distance } = calculateDragDistance(startX, startY, currentX, currentY);
    return distance >= threshold;
  };

  // 计算频率变化（基于垂直拖拽距离）
  const calculateFrequencyChange = (
    deltaY: number,
    sensitivity: number = 2
  ): number => {
    // 向上为正，向下为负
    // 每10px变化对应sensitivity * 10个单位的频率变化
    return -(deltaY / 10) * sensitivity * 10;
  };

  // 限制数值在指定范围内
  const clampValue = (value: number, min: number, max: number): number => {
    return Math.max(min, Math.min(max, value));
  };

  // 将时间值向下取整到整数
  const floorTime = (time: number): number => {
    return Math.floor(Math.max(0, time));
  };

  // 检查音频时长约束
  const checkAudioDurationConstraint = (
    stopTime: number,
    audioDuration: number | null
  ): BoundaryCheckResult => {
    if (audioDuration === null) {
      return { isWithinBounds: true };
    }

    if (stopTime > audioDuration) {
      return {
        isWithinBounds: false,
        adjustedValue: audioDuration,
        boundaryType: 'duration'
      };
    }

    return { isWithinBounds: true };
  };

  // 计算原始强度比例
  const calculateRawIntensityRatio = (
    drawIntensity: number,
    eventIntensity: number
  ): number => {
    if (eventIntensity === 0) return 0;
    return Math.max(0, Math.min(1, drawIntensity / eventIntensity));
  };

  /**
   * 智能强度调整：当目标强度超过全局强度时，自动提升全局强度
   * @param targetIntensity 目标Curve点强度
   * @param currentGlobalIntensity 当前全局强度
   * @param options 配置选项
   * @returns 调整结果
   */
  const handleIntensityWithAutoGlobalAdjustment = (
    targetIntensity: number,
    currentGlobalIntensity: number,
    options: {
      buffer?: number; // 全局强度缓冲比例，默认5%
      maxIntensity?: number; // 最大全局强度，默认100
      enableAutoAdjust?: boolean; // 是否启用自动调整，默认true
    } = {}
  ): {
    needsGlobalUpdate: boolean;
    newGlobalIntensity: number;
    adjustedCurveIntensity: number;
    originalTargetIntensity: number;
  } => {
    const {
      buffer = 0.05,
      maxIntensity = 100,
      enableAutoAdjust = true
    } = options;

    // 如果未启用自动调整或目标强度不超过当前全局强度，不需要调整
    if (!enableAutoAdjust || targetIntensity <= currentGlobalIntensity) {
      return {
        needsGlobalUpdate: false,
        newGlobalIntensity: currentGlobalIntensity,
        adjustedCurveIntensity: Math.min(targetIntensity, currentGlobalIntensity),
        originalTargetIntensity: targetIntensity
      };
    }

    // 计算新的全局强度：目标强度 + 缓冲，但不超过最大值
    const newGlobalIntensity = Math.min(
      Math.ceil(targetIntensity * (1 + buffer)),
      maxIntensity
    );

    // 确保新的全局强度确实大于当前值
    const finalGlobalIntensity = Math.max(newGlobalIntensity, currentGlobalIntensity);

    return {
      needsGlobalUpdate: finalGlobalIntensity > currentGlobalIntensity,
      newGlobalIntensity: finalGlobalIntensity,
      adjustedCurveIntensity: Math.min(targetIntensity, finalGlobalIntensity),
      originalTargetIntensity: targetIntensity
    };
  };

  // 验证拖拽距离是否足够开始拖拽
  const validateDragThreshold = (
    startX: number,
    startY: number,
    currentX: number,
    currentY: number,
    threshold: number = 5
  ): boolean => {
    return shouldStartDrag(startX, startY, currentX, currentY, threshold);
  };

  return {
    // 边界和碰撞检查
    checkTimeBoundary,
    checkEventCollision,
    checkAudioDurationConstraint,

    // 值验证
    validateIntensity,
    validateFrequency,
    validateRelativeFrequency,
    validateTimeOffset,
    validateDragThreshold,

    // 智能强度调整
    handleIntensityWithAutoGlobalAdjustment,

    // 计算辅助函数
    calculateCurvePointBounds,
    calculateDragDistance,
    calculateFrequencyChange,
    calculateRawIntensityRatio,
    shouldStartDrag,

    // 工具函数
    clampValue,
    floorTime,
  };
}
