<template>
  <div class="waveform-container" ref="waveformContainerRef">
    <!-- 单个he文件控制区 -->
    <div v-if="effect" class="he-file-controls">
      <DurationAdjustPanel
        :audio-duration="props.audioDuration"
        @increase-duration="handleIncreaseDuration"
        :is-timeline-adjustable="waveformStore.isTimelineDurationAdjustable"
        :current-total-duration="waveformStore.currentTotalDuration"
        :file-uuid="props.fileUuid"
      />
      <div class="right-controls">
        <!-- 撤销按钮 -->
        <UndoButton />
        <!-- 重做按钮 -->
        <RedoButton />
        <!-- 保存按钮 -->
        <SaveButton />
        <!-- 关闭标签页按钮 -->
        <CloseTabButton v-show="false" @close-tab="handleCloseTab" />
      </div>
    </div>

    <n-empty v-if="!effect" :description="t('editor.waveform.noFileSelected')" class="waveform-empty-center" />
    <div v-else class="waveform-content">
      <div class="waveform-graph-container">
        <WaveformGraph
          :file-uuid="validatedFileUuid"
          :time-window="{ start: 0, end: waveformStore.currentTotalDuration }"
          :total-effect-duration="waveformStore.currentTotalDuration"
          :baseline-duration="defaultDuration"
          :available-parent-width="waveformContainerWidth"
          :audio-duration="props.audioDuration"
          :show-audio-waveform="true"
          :is-using-cached-data="isUsingCachedData"
          :ui-state-sync="uiStateSync"
          @visible-time-range-changed="handleTimeRangeChanged"
        />
      </div>


    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, ref, watch, onMounted, onBeforeUnmount, defineEmits } from "vue";
import WaveformGraph from "./InteractiveWaveformCanvas.vue";
import DurationAdjustPanel from "../adjustments/TimelineDurationPanel.vue";
import UndoButton from "../common/UndoButton.vue";
import RedoButton from "../common/RedoButton.vue";
import SaveButton from "../common/FileSaveButton.vue";
import CloseTabButton from "../common/TabCloseButton.vue";
import type { RealityTapEffect } from "@/types/haptic-file";
import type { RenderableEvent } from "@/types/haptic-editor";
import { validateFileUuid } from "@/stores/haptics-editor-store";
import { useProjectStore } from "@/stores/haptics-project-store";
import { flattenRealityTapEffect } from "@/utils/haptic-event-processor";
import { useFileStateSync } from "@/composables/useFileStateSync";
import { useWaveformUIStateSync } from "@/composables/useWaveformUIStateSync";
import { useI18n } from "@/composables/useI18n";
import { NEmpty } from "naive-ui";
import { logger, LogModule } from "@/utils/logger/logger";

// 音频振幅数据接口已移动到 Store 中

const props = withDefaults(defineProps<{
  fileUuid: string; // 必需：文件 UUID，用于文件级别状态隔离
  effect: RealityTapEffect | null; // Allow null initially
  defaultTimeWindowDuration?: number; // 添加可配置的默认时间窗口
  audioDuration?: number | null; // 音频时长（ms），有音频时传递，无音频时为null
}>(), {
  defaultTimeWindowDuration: 5000
});

const emit = defineEmits<{
  (e: "increase-duration", durationInMs: number): void;
  (e: "close-tab"): void;
}>();

const { t } = useI18n();

// 导入统一的全屏时长常量
import { FULL_SCREEN_TIME_MS } from "./config/waveform-constants";

// 设置defaultTimeWindowDuration的默认值
const defaultDuration = props.defaultTimeWindowDuration ?? FULL_SCREEN_TIME_MS;

// 获取stores - 使用文件级状态同步
const projectStore = useProjectStore();
// 验证文件UUID
const validatedFileUuid = validateFileUuid(props.fileUuid);

// 使用文件级状态同步
const fileStateSync = useFileStateSync({
  fileUuid: validatedFileUuid,
  enableLogging: true,
});

// 使用UI状态缓存
const uiStateSync = useWaveformUIStateSync({
  fileUuid: validatedFileUuid,
  enableAutoSave: true,
  debugMode: true,
});

// 从状态同步中获取 store
const { store: waveformStore } = fileStateSync;
logger.info(LogModule.WAVEFORM, `WaveformEditor: 使用增强型文件级别状态管理，fileUuid: ${validatedFileUuid}`);

// Internal State
const processedEvents = ref<RenderableEvent[]>([]); // To store events from flattenRealityTapEffect
const waveformContainerRef = ref<HTMLDivElement | null>(null); // Added for check 9
const waveformContainerWidth = ref(800); // 【修复】提供默认值，避免初始值为 0



// 添加一个标记来跟踪是否使用了缓存数据
const isUsingCachedData = ref(false);

// 时间范围同步状态
const currentVisibleStartTime = ref(0);
const currentVisibleEndTime = ref(defaultDuration);

// Watch for changes in the input effect data
watch(
  () => props.effect,
  (newEffect) => {
    if (!newEffect) {
      processedEvents.value = [];
      waveformStore.value.setEvents([]);
      isUsingCachedData.value = false;
      return;
    }

    // 检查是否有缓存的事件数据 - 使用当前文件UUID
    const currentFileUuid = validatedFileUuid;
    const cachedData = projectStore.getFileCache(currentFileUuid);

    // 【修复】只有在缓存有实际意义时才使用缓存（避免使用空缓存覆盖原始数据）
    if (cachedData && (cachedData.isModified || cachedData.currentEvents.length > 0)) {
      // 使用缓存的事件数据（有修改或有事件数据）
      logger.info(LogModule.WAVEFORM, `WaveformEditor[${currentFileUuid}]: 使用缓存数据，事件数量: ${cachedData.currentEvents.length}，修改状态: ${cachedData.isModified}`);
      processedEvents.value = cachedData.currentEvents;

      // 使用 setEvents 并传递文件加载选项
      waveformStore.value.setEvents(cachedData.currentEvents, {
        isFileInitialLoad: true,
        source: 'cache_restore',
        skipHistoryRecord: true // 缓存恢复时跳过历史记录
      });
      isUsingCachedData.value = true;

      // 使用原始数据计算持续时间
      const { duration: effectNaturalDuration } = flattenRealityTapEffect(cachedData.originalData || newEffect);
      waveformStore.value.updateBaseDurations(effectNaturalDuration, props.audioDuration ?? null);
    } else {
      // 使用原始文件数据
      const { events, duration: effectNaturalDuration } = flattenRealityTapEffect(newEffect);
      processedEvents.value = events;

      // 使用 setEvents 并传递文件加载选项
      waveformStore.value.setEvents(events, {
        isFileInitialLoad: true,
        source: 'file_load',
        skipHistoryRecord: true // 文件初次加载时跳过历史记录
      });
      waveformStore.value.updateBaseDurations(effectNaturalDuration, props.audioDuration ?? null);
      isUsingCachedData.value = false;
      logger.info(LogModule.WAVEFORM, `WaveformEditor[${currentFileUuid}]: 使用原始文件数据，事件数量: ${events.length}`);

      // 【修复】不在此处创建缓存，避免在文件关闭后重新打开时立即重建缓存
      // 缓存应该只在文件切换时由 ProjectEditor.vue 创建，而不是在 WaveformEditor 中创建
      logger.debug(LogModule.WAVEFORM, `WaveformEditor[${currentFileUuid}]: 使用原始数据，不创建缓存 (避免缓存释放问题)`);
    }
  },
  { immediate: true, deep: true }
);

watch(
  () => props.audioDuration,
  (newAudioDuration) => {
    const currentEffectNaturalDuration = props.effect ? flattenRealityTapEffect(props.effect).duration : 0;
    waveformStore.value.updateBaseDurations(currentEffectNaturalDuration, newAudioDuration ?? null);
  },
  { immediate: true }
);

let resizeObserver: ResizeObserver | null = null;

onMounted(() => {
  if (waveformContainerRef.value) {
    // 【修复】立即设置初始宽度，避免传递 0 值
    const initialWidth = waveformContainerRef.value.clientWidth;
    waveformContainerWidth.value = initialWidth > 0 ? initialWidth : 800; // 提供默认值

    logger.debug(LogModule.WAVEFORM, `[HapticWaveformEditor] 初始容器宽度: ${waveformContainerWidth.value}px`);

    resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        if (entry.target === waveformContainerRef.value) {
          const newWidth = entry.contentRect.width;
          if (newWidth > 0 && newWidth !== waveformContainerWidth.value) {
            waveformContainerWidth.value = newWidth;
            logger.debug(LogModule.WAVEFORM, `[HapticWaveformEditor] 容器宽度变化: ${newWidth}px`);
          }
        }
      }
    });
    resizeObserver.observe(waveformContainerRef.value);
  }
});

onBeforeUnmount(() => {
  if (resizeObserver && waveformContainerRef.value) {
    resizeObserver.unobserve(waveformContainerRef.value);
  }
  if (resizeObserver) {
    resizeObserver.disconnect();
  }
});

// 事件处理器
const handleIncreaseDuration = (durationInMs: number) => {
  // 只传递事件给父组件，让父组件处理实际的逻辑
  emit("increase-duration", durationInMs);
};

const handleCloseTab = () => {
  emit("close-tab");
};

// 处理时间范围变化事件
const handleTimeRangeChanged = (startTime: number, endTime: number) => {
  currentVisibleStartTime.value = startTime;
  currentVisibleEndTime.value = endTime;

  logger.debug(LogModule.WAVEFORM, '[HapticWaveformEditor] 时间范围变化:', {
    startTime: startTime.toFixed(2) + 'ms',
    endTime: endTime.toFixed(2) + 'ms',
    duration: (endTime - startTime).toFixed(2) + 'ms'
  });
};

// 监听容器尺寸变化，更新UI状态
watch(waveformContainerWidth, (newWidth) => {
  if (newWidth > 0) {
    uiStateSync.setContainerWidth(newWidth);
  }
}, { immediate: true });
</script>

<style scoped>
.waveform-container {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.he-file-controls {
  width: 100%;
  height: 60px;
  min-height: 60px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 12px;
  margin-bottom: 10px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
}

.right-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.waveform-empty-center {
  width: 100%;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  font-size: 1.1rem;
  color: #bbb;
}

.waveform-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
  width: 100%;
  flex: 1;
  min-height: 0; /* 允许flex子项收缩 */
}

.waveform-graph-container {
  flex: 1; /* 占用剩余空间 */
  min-height: 300px; /* 最小高度确保可见性 */
  display: flex;
  flex-direction: column;
}

.readonly-audio-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
  background: rgba(255, 255, 255, 0.02);
  border-radius: 8px;
  padding: 12px;
  border: 1px solid rgba(255, 255, 255, 0.05);
  flex-shrink: 0; /* 不允许收缩 */
  min-height: 120px; /* 为音频波形设置最小高度 */
}

.audio-section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 4px;
}

.audio-section-title {
  font-size: 14px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  gap: 6px;
}

.audio-section-title::before {
  content: "🎵";
  font-size: 12px;
}
</style>
