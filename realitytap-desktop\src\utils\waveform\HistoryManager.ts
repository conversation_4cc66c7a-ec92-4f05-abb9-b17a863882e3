/**
 * 历史记录管理器（简化版本）
 */

import { v4 as uuidv4 } from 'uuid';
import type { RenderableEvent } from '@/types/haptic-editor';
import { logger, LogModule } from '@/utils/logger/logger';

/**
 * 历史记录项
 */
export interface HistoryRecord {
  id: string;
  timestamp: number;
  data: RenderableEvent[];
  description?: string;
}

/**
 * 历史管理器配置
 */
export interface HistoryConfig {
  /** 最大历史记录数量，默认50 */
  maxRecords?: number;
  /** 是否启用调试模式，默认false */
  enableDebug?: boolean;
  /** 是否启用智能变化检测，默认true */
  enableChangeDetection?: boolean;
  /** 变化检测阈值，默认0.001 */
  changeThreshold?: number;
  /** 自定义数据比较器 */
  customComparator?: (oldData: RenderableEvent[], newData: RenderableEvent[]) => boolean;
  /** 自定义深拷贝函数 */
  customDeepClone?: (data: RenderableEvent[]) => RenderableEvent[];
  /** 是否为文件初次加载场景，默认false */
  isFileInitialLoad?: boolean;
  /** 数据来源，用于优化日志和处理逻辑 */
  source?: 'user_action' | 'file_load' | 'cache_restore' | 'auto_sync';
}

/**
 * 历史管理器状态
 */
export interface HistoryState {
  canUndo: boolean;
  canRedo: boolean;
  undoStackSize: number;
  redoStackSize: number;
  totalRecords: number;
  lastOperationTimestamp: number | null;
}

/**
 * 默认配置
 */
const DEFAULT_CONFIG: Required<HistoryConfig> = {
  maxRecords: 50,
  enableDebug: false,
  enableChangeDetection: true,
  changeThreshold: 0.001,
  customComparator: (oldData, newData) => !areEventsEqual(oldData, newData),
  customDeepClone: (data) => deepCloneEvents(data),
  isFileInitialLoad: false,
  source: 'user_action'
};

/**
 * 历史记录管理器
 */
export class HistoryManager {
  private undoStack: HistoryRecord[] = [];
  private redoStack: HistoryRecord[] = [];
  private config: Required<HistoryConfig>;
  private isDestroyed = false;
  private lastRecordedDataHash = '';

  constructor(config?: HistoryConfig) {
    this.config = { ...DEFAULT_CONFIG, ...config };
    
    if (this.config.enableDebug) {
      logger.info(LogModule.HISTORY, 'HistoryManager: 构造函数完成', {
        config: this.config
      });
    }
  }

  /**
   * 初始化历史管理器
   */
  initialize(initialData: RenderableEvent[]): void {
    if (this.isDestroyed) return;

    // 清空现有记录
    this.undoStack = [];
    this.redoStack = [];

    // 添加初始状态
    const success = this.addRecord(initialData, '初始状态');
    
    if (this.config.enableDebug) {
      logger.info(LogModule.HISTORY, 'HistoryManager: initialize() 完成', {
        success,
        initialDataLength: initialData.length
      });
    }
  }

  /**
   * 添加历史记录
   */
  addRecord(data: RenderableEvent[], description?: string, options?: { source?: string; isFileInitialLoad?: boolean }): boolean {
    if (this.isDestroyed) {
      return false;
    }

    try {
      // 智能变化检测
      if (this.config.enableChangeDetection && !this.hasSignificantChanges(data)) {
        // 根据数据来源调整日志级别
        const isFileLoadScenario = options?.isFileInitialLoad ||
                                   options?.source === 'file_load' ||
                                   options?.source === 'cache_restore';

        if (isFileLoadScenario) {
          // 文件加载场景：使用 DEBUG 级别，提供更友好的信息
          logger.debug(LogModule.HISTORY, '🎯 文件加载时检测到重复数据，智能跳过历史记录添加', {
            description,
            dataLength: data.length,
            source: options?.source || 'unknown',
            reason: '这是正常的数据保护机制，防止重复记录'
          });
        } else if (this.config.enableDebug) {
          // 用户操作场景：保持原有的调试日志
          logger.debug(LogModule.HISTORY, 'HistoryManager: 数据无显著变化，跳过记录', {
            description,
            dataLength: data.length,
            undoStackSize: this.undoStack.length,
            source: options?.source || 'user_action',
            lastRecordedDataHashPrefix: this.lastRecordedDataHash?.substring(0, 20) + '...',
            currentDataHashPrefix: this.calculateDataHash(data).substring(0, 20) + '...'
          });
        }
        return false;
      }

      // 深拷贝数据
      const clonedData = this.config.customDeepClone(data);
      
      // 创建历史记录
      const record: HistoryRecord = {
        id: uuidv4(),
        timestamp: Date.now(),
        data: clonedData,
        description
      };

      // 添加到撤销栈
      this.undoStack.push(record);

      // 清空重做栈
      if (this.redoStack.length > 0) {
        this.redoStack = [];
      }

      // 检查栈大小限制
      if (this.undoStack.length > this.config.maxRecords) {
        this.undoStack.shift();
      }

      // 更新数据哈希
      this.lastRecordedDataHash = this.calculateDataHash(data);

      if (this.config.enableDebug) {
        logger.info(LogModule.HISTORY, 'HistoryManager: 添加记录成功', {
          recordId: record.id,
          description,
          undoStackSize: this.undoStack.length,
          dataLength: data.length
        });
      }

      return true;
    } catch (error) {
      logger.error(LogModule.HISTORY, 'HistoryManager: 添加记录失败', error);
      return false;
    }
  }

  /**
   * 撤销操作
   */
  undo(): RenderableEvent[] | null {
    if (this.isDestroyed || !this.canUndo()) {
      return null;
    }

    try {
      const currentRecord = this.undoStack.pop()!;
      
      // 获取撤销后的数据
      let resultData: RenderableEvent[];
      
      if (this.undoStack.length > 0) {
        // 移动当前记录到重做栈
        this.redoStack.push(currentRecord);
        // 返回上一个记录的数据
        resultData = this.config.customDeepClone(this.undoStack[this.undoStack.length - 1].data);
      } else {
        // 已经是初始状态，将记录放回撤销栈
        this.undoStack.push(currentRecord);
        resultData = this.config.customDeepClone(currentRecord.data);
        
        if (this.config.enableDebug) {
          logger.info(LogModule.HISTORY, 'HistoryManager: 已经是初始状态，无法继续撤销');
        }
        return null;
      }

      if (this.config.enableDebug) {
        logger.info(LogModule.HISTORY, 'HistoryManager: 撤销操作成功', {
          recordId: currentRecord.id,
          description: currentRecord.description,
          undoStackSize: this.undoStack.length,
          redoStackSize: this.redoStack.length,
          resultDataLength: resultData.length
        });
      }

      return resultData;
    } catch (error) {
      logger.error(LogModule.HISTORY, 'HistoryManager: 撤销操作失败', error);
      return null;
    }
  }

  /**
   * 重做操作
   */
  redo(): RenderableEvent[] | null {
    if (this.isDestroyed || !this.canRedo()) {
      return null;
    }

    try {
      const record = this.redoStack.pop()!;
      this.undoStack.push(record);

      const resultData = this.config.customDeepClone(record.data);

      if (this.config.enableDebug) {
        logger.info(LogModule.HISTORY, 'HistoryManager: 重做操作成功', {
          recordId: record.id,
          description: record.description,
          undoStackSize: this.undoStack.length,
          redoStackSize: this.redoStack.length,
          resultDataLength: resultData.length
        });
      }

      return resultData;
    } catch (error) {
      logger.error(LogModule.HISTORY, 'HistoryManager: 重做操作失败', error);
      return null;
    }
  }

  /**
   * 检查是否可以撤销
   */
  canUndo(): boolean {
    return !this.isDestroyed && this.undoStack.length > 1;
  }

  /**
   * 检查是否可以重做
   */
  canRedo(): boolean {
    return !this.isDestroyed && this.redoStack.length > 0;
  }

  /**
   * 清空所有历史记录
   */
  clear(): void {
    if (this.isDestroyed) return;
    
    this.undoStack = [];
    this.redoStack = [];
    this.lastRecordedDataHash = '';
    
    if (this.config.enableDebug) {
      logger.info(LogModule.HISTORY, 'HistoryManager: 清空历史记录');
    }
  }

  /**
   * 获取当前状态
   */
  getState(): HistoryState {
    return {
      canUndo: this.canUndo(),
      canRedo: this.canRedo(),
      undoStackSize: this.undoStack.length,
      redoStackSize: this.redoStack.length,
      totalRecords: this.undoStack.length + this.redoStack.length,
      lastOperationTimestamp: this.undoStack.length > 0 ? this.undoStack[this.undoStack.length - 1].timestamp : null
    };
  }

  /**
   * 配置历史管理器
   */
  configure(options: HistoryConfig): void {
    if (this.isDestroyed) return;
    
    this.config = { ...this.config, ...options };
    
    if (this.config.enableDebug) {
      logger.info(LogModule.HISTORY, 'HistoryManager: 配置已更新', { config: this.config });
    }
  }

  /**
   * 销毁历史管理器
   */
  destroy(): void {
    this.clear();
    this.isDestroyed = true;
    
    if (this.config.enableDebug) {
      logger.info(LogModule.HISTORY, 'HistoryManager: 已销毁');
    }
  }

  /**
   * 检查数据是否有显著变化
   */
  private hasSignificantChanges(data: RenderableEvent[]): boolean {
    if (this.undoStack.length === 0) {
      return true; // 第一次记录
    }

    const currentHash = this.calculateDataHash(data);

    if (this.config.enableDebug) {
      logger.debug(LogModule.HISTORY, `hasSignificantChanges 检查`, {
        currentHash: currentHash.substring(0, 100) + '...',
        lastRecordedDataHash: this.lastRecordedDataHash?.substring(0, 100) + '...',
        hashesEqual: currentHash === this.lastRecordedDataHash
      });
    }

    if (currentHash === this.lastRecordedDataHash) {
      if (this.config.enableDebug) {
        logger.debug(LogModule.HISTORY, 'HistoryManager: 哈希值相同，数据无变化');
      }
      return false; // 数据完全相同
    }

    // 使用自定义比较器
    const lastData = this.undoStack[this.undoStack.length - 1].data;
    return this.config.customComparator(lastData, data);
  }

  /**
   * 计算数据哈希值（高效实现，避免JSON序列化）
   */
  private calculateDataHash(data: RenderableEvent[]): string {
    let hash = `${data.length}|`;

    for (const event of data) {
      hash += `${event.id}:${event.type}:${Math.round(event.startTime * 1000)}`;

      if (event.type === 'transient') {
        const transientEvent = event as any;
        hash += `:${Math.round((transientEvent.intensity || 0) * 1000)}`;
        hash += `:${Math.round((transientEvent.frequency || 0) * 1000)}`;
      } else if (event.type === 'continuous') {
        const continuousEvent = event as any;
        hash += `:${Math.round((continuousEvent.eventIntensity || 0) * 1000)}`;
        hash += `:${Math.round((continuousEvent.eventFrequency || 0) * 1000)}`;
        hash += `:${Math.round((continuousEvent.duration || 0) * 1000)}`;

        // 包含curves信息
        if (continuousEvent.curves && Array.isArray(continuousEvent.curves)) {
          hash += `:curves[${continuousEvent.curves.length}]`;
          for (const curve of continuousEvent.curves) {
            hash += `(${Math.round(curve.timeOffset * 1000)},${Math.round((curve.drawIntensity || 0) * 1000)})`;
          }
        }
      }

      hash += '|';
    }

    return hash;
  }
}

/**
 * 高效的事件数组深度克隆（避免JSON序列化）
 */
function deepCloneEvents(events: RenderableEvent[]): RenderableEvent[] {
  return events.map(event => {
    const clonedEvent: any = {
      id: event.id,
      startTime: event.startTime,
      type: event.type
    };

    if (event.type === 'transient') {
      const transientEvent = event as any;
      clonedEvent.intensity = transientEvent.intensity;
      clonedEvent.frequency = transientEvent.frequency;
      clonedEvent.peakTime = transientEvent.peakTime;
      clonedEvent.stopTime = transientEvent.stopTime;

      // 克隆其他可能的属性
      if (transientEvent.waveform) clonedEvent.waveform = transientEvent.waveform;
      if (transientEvent.sharpness !== undefined) clonedEvent.sharpness = transientEvent.sharpness;

    } else if (event.type === 'continuous') {
      const continuousEvent = event as any;
      clonedEvent.eventIntensity = continuousEvent.eventIntensity;
      clonedEvent.eventFrequency = continuousEvent.eventFrequency;
      clonedEvent.duration = continuousEvent.duration;
      clonedEvent.stopTime = continuousEvent.stopTime;

      // 深度克隆curves数组，包含所有必要属性
      if (continuousEvent.curves && Array.isArray(continuousEvent.curves)) {
        clonedEvent.curves = continuousEvent.curves.map((curve: any) => ({
          timeOffset: curve.timeOffset,
          drawIntensity: curve.drawIntensity,
          rawIntensity: curve.rawIntensity,
          relativeCurveFrequency: curve.relativeCurveFrequency,
          curveFrequency: curve.curveFrequency,
          // 保持向后兼容性
          intensity: curve.intensity,
          frequency: curve.frequency
        }));
      }

      // 克隆其他可能的属性
      if (continuousEvent.waveform) clonedEvent.waveform = continuousEvent.waveform;
    }

    return clonedEvent as RenderableEvent;
  });
}

/**
 * 比较两个事件数组是否相等
 */
function areEventsEqual(events1: RenderableEvent[], events2: RenderableEvent[]): boolean {
  if (events1.length !== events2.length) {
    return false;
  }

  return events1.every((event1, index) => {
    const event2 = events2[index];

    // 基础字段比较
    if (event1.id !== event2.id ||
        Math.abs(event1.startTime - event2.startTime) >= 0.001 ||
        event1.type !== event2.type) {
      return false;
    }

    // 根据事件类型比较特定字段
    if (event1.type === 'transient' && event2.type === 'transient') {
      const t1 = event1 as any;
      const t2 = event2 as any;
      return Math.abs((t1.intensity || 0) - (t2.intensity || 0)) < 0.001 &&
             Math.abs((t1.frequency || 0) - (t2.frequency || 0)) < 0.001;
    } else if (event1.type === 'continuous' && event2.type === 'continuous') {
      const c1 = event1 as any;
      const c2 = event2 as any;

      // 比较基本属性
      if (Math.abs((c1.eventIntensity || 0) - (c2.eventIntensity || 0)) >= 0.001 ||
          Math.abs((c1.eventFrequency || 0) - (c2.eventFrequency || 0)) >= 0.001 ||
          Math.abs((c1.duration || 0) - (c2.duration || 0)) >= 0.001) {
        return false;
      }

      // 比较curves数组
      if (!c1.curves || !c2.curves || c1.curves.length !== c2.curves.length) {
        return false;
      }

      // 比较每个curve点
      for (let i = 0; i < c1.curves.length; i++) {
        const curve1 = c1.curves[i];
        const curve2 = c2.curves[i];

        if (Math.abs((curve1.timeOffset || 0) - (curve2.timeOffset || 0)) >= 0.001 ||
            Math.abs((curve1.drawIntensity || 0) - (curve2.drawIntensity || 0)) >= 0.001 ||
            Math.abs((curve1.rawIntensity || 0) - (curve2.rawIntensity || 0)) >= 0.001 ||
            Math.abs((curve1.curveFrequency || 0) - (curve2.curveFrequency || 0)) >= 0.001 ||
            Math.abs((curve1.relativeCurveFrequency || 0) - (curve2.relativeCurveFrequency || 0)) >= 0.001) {
          return false;
        }
      }

      return true;
    }

    return true;
  });
}
