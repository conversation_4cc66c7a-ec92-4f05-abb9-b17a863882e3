<template>
  <div class="waveform-graph" ref="graphContainer">
    <YAxisArea
      ref="yAxisAreaRef"
      :canvasHeight="canvasHeight"
      :paddingTop="PADDING.top"
      :paddingBottom="PADDING.bottom"
      :paddingLeft="PADDING.left"
      :axisColor="AXIS_COLOR"
      :labelColor="AXIS_LABEL_COLOR"
      :fontStyle="FONT_STYLE"
    />
    <n-scrollbar x-scrollable ref="horizontalScrollbarRef" x-placement="top" @scroll="eventListeners.onHorizontalScroll" class="waveform-scrollbar">
      <div class="canvas-container" ref="canvasContainer">
        <canvas ref="waveformCanvas" class="waveform-canvas"></canvas>
      </div>
    </n-scrollbar>
    <!-- 使用提取出的事件菜单组件 -->
    <EventMenu
      :visible="isCanvasMenuVisible"
      :position="canvasMenuPosition"
      :timePosition="canvasMenuTimePosition"
      :availableSpaceText="availableSpaceText"
      :canAddTransient="canAddTransient"
      :canAddContinuous="canAddContinuous"
      :contextMenuEventId="contextMenuEventId"
      :minTransientSpace="MIN_TRANSIENT_DURATION"
      :minContinuousSpace="MIN_CONTINUOUS_DURATION"
      @add-transient="eventListeners.handleAddTransientEvent"
      @add-continuous="eventListeners.handleAddContinuousEvent"
      @delete-event="eventListeners.handleDeleteEvent"
      @mouse-enter="eventListeners.handleMenuMouseEnter"
      @mouse-leave="eventListeners.handleMenuMouseLeave"
      @close="eventListeners.closeCanvasMenu"
    />
    <!-- 事件值显示tooltip -->
    <EventTooltip ref="eventTooltipRef" :config="eventTooltipConfig" />
  </div>
</template>

<script setup lang="ts">
import { useFileWaveformEditorStore, validateFileUuid } from "@/stores/haptics-editor-store";
import { useProjectStore } from "@/stores/haptics-project-store";
import type { RenderableEvent } from "@/types/haptic-editor";
import type { ScrollbarInst } from "naive-ui";
import { defineEmits, defineProps, ref, onBeforeUnmount, nextTick, watch, computed, toRef } from "vue";
import EventMenu from "./components/WaveformContextMenu.vue";
import EventTooltip from "./components/WaveformValueTooltip.vue";
import YAxisArea from "./components/WaveformYAxisScale.vue";
// WaveformPerformanceMonitor 已移除

// 导入新的 composable 管理器
import { useWaveformWatchers } from "./composables/useWaveformWatchers";
import { useWaveformCleanup } from "./composables/useWaveformCleanup";
import { useWaveformDrawingOrchestrator } from "./composables/useWaveformDrawingOrchestrator";
import { useWaveformRenderingPipeline } from "./composables/useWaveformRenderingPipeline";

// 导入新的数据访问系统
import { useWaveformProvider } from "./composables/useWaveformProvider";

// 导入必要的 composables
import { useAsyncOperationManager } from "./composables/useAsyncOperationManager";
import { useDragHandler } from "./composables/useDragHandler";
import { useWaveformCache } from "./composables/useWaveformCache";
import { useWaveformCanvas } from "./composables/useWaveformCanvas";
import { useWaveformClickHandler } from "./composables/useWaveformClickHandler";
import { useWaveformContextMenu } from "./composables/useWaveformContextMenu";
import { useWaveformCoordinate } from "./composables/useWaveformCoordinate";
import { useWaveformEventDrawing } from "./composables/useWaveformEventDrawing";
import { useWaveformEventListeners } from "./composables/useWaveformEventListeners";
import { useWaveformGrid } from "./composables/useWaveformGrid";
import { useWaveformGuides } from "./composables/useWaveformGuides";
import { useWaveformKeyboard } from "./composables/useWaveformKeyboard";
import { useWaveformLifecycle } from "./composables/useWaveformLifecycle";
import { useWaveformZoom } from "./composables/useWaveformZoom";
import { useVisibleTimeRange } from "./composables/useVisibleTimeRange";
import { useAudioWaveform } from "./composables/useAudioWaveform";

// 导入工具函数和常量
import { calculateTargetGraphWidth } from "./utils/coordinate";
import { AXIS_COLOR, AXIS_LABEL_COLOR, FONT_STYLE } from "./utils/color";
import { AMPLITUDE_PRESETS } from "./utils/audio-amplitude-helpers";
import { WAVEFORM_SAFE_OFFSET } from "./utils/drawing-helpers";
import { FULL_SCREEN_TIME_MS, LAYOUT_CONSTANTS } from "./config/waveform-constants";

// 导入类型定义
import type { WaveformCacheConfig } from "./composables/useWaveformCache";
import type { ContextMenuConfig } from "./composables/useWaveformContextMenu";
import type { KeyboardConfig } from "./composables/useWaveformKeyboard";
import type { EventListenerConfig } from "./composables/useWaveformEventListeners";
import type { LifecycleConfig, LifecycleEventHandlers, LifecycleRefs } from "./composables/useWaveformLifecycle";

// 历史记录系统已直接集成到Store中

// 导入缩放持久化工具
import { createZoomPersistence } from "@/utils/waveform/zoomPersistence";
import { logger, LogModule } from "@/utils/logger/logger";

// 音频振幅数据接口已移动到 Store 中

const props = withDefaults(
  defineProps<{
    fileUuid: string; // 必需：文件UUID，用于文件级别状态隔离
    totalEffectDuration: number;
    baselineDuration: number;
    availableParentWidth: number;
    audioDuration?: number | null; // 音频时长（ms），有音频时传递，无音频时为null
    showAudioWaveform?: boolean; // 是否显示音频波形，默认 true
    isUsingCachedData?: boolean; // 是否使用缓存数据
    amplitudeDisplayMode?: "conservative" | "standard" | "enhanced" | "maximum"; // 振幅显示模式，默认 'standard'
  }>(),
  {
    showAudioWaveform: true,
    isUsingCachedData: false,
    amplitudeDisplayMode: "standard",
  }
);

// 使用文件级别的 Store - 强制要求fileUuid
const validatedFileUuid = validateFileUuid(props.fileUuid);
const waveformStore = useFileWaveformEditorStore(validatedFileUuid);
const projectStore = useProjectStore();
logger.debug(LogModule.WAVEFORM, "WaveformGraph: 使用文件级别store", { fileUuid: validatedFileUuid });

// 设置数据提供者，为子组件提供统一的数据访问接口
const { dataAccess } = useWaveformProvider({
  waveformStore,
  fileUuid: validatedFileUuid,
  enableCache: true,
  enableLogging: false, // 生产环境关闭日志
});

// 使用异步操作管理器
const asyncManager = useAsyncOperationManager();

const emit = defineEmits<{
  (e: "event-selected", id: string | null): void;
  (e: "graph-wheel-scroll", delta: number): void;
  (e: "visible-time-range-changed", startTime: number, endTime: number): void;
}>();

// DOM 引用
const graphContainer = ref<HTMLDivElement | null>(null);
const canvasContainer = ref<HTMLDivElement | null>(null);
const waveformCanvas = ref<HTMLCanvasElement | null>(null);
const canvasCtx = ref<CanvasRenderingContext2D | null>(null);
const yAxisAreaRef = ref<InstanceType<typeof YAxisArea> | null>(null);
const horizontalScrollbarRef = ref<ScrollbarInst | null>(null);

// 使用点击处理 Composable，传递文件级别的 store
const clickHandler = useWaveformClickHandler(waveformStore);

// Constants for styling and layout - 使用统一的常量配置确保一致性
const PADDING = LAYOUT_CONSTANTS.DEFAULT_PADDING; // 使用常量文件中的默认padding配置

// 先声明缩放相关变量，稍后初始化
let zoomManager: any;
const currentZoomLevel = ref(1.0); // 使用默认缩放级别

// 创建缩放率持久化管理器
const zoomPersistence = createZoomPersistence(validatedFileUuid, false); // 关闭调试模式

// 使用画布管理组合式函数
const canvasConfig = {
  padding: PADDING,
  safeOffset: WAVEFORM_SAFE_OFFSET,
};

const {
  canvasWidth,
  canvasHeight,
  logicalCanvasWidth,
  scrollLeftValue,
  virtualScrollOffset,
  getEffectiveDuration,
  getGraphAreaWidth,
  getLogicalGraphAreaWidth,
  getGraphAreaHeight,
  updateVirtualScrollOffset,
  updateCanvasPosition,
  resizeCanvas,
  checkAndFixScrollBoundary,
} = useWaveformCanvas(props, canvasConfig, currentZoomLevel);

// 使用坐标管理组合式函数（支持缩放）
const canvasState = {
  canvasWidth,
  canvasHeight,
  virtualScrollOffset,
  getEffectiveDuration,
  getGraphAreaWidth,
  getLogicalGraphAreaWidth,
  getGraphAreaHeight,
};

const { mapTimeToXLocal, mapIntensityToYLocal, mapYToIntensityLocal, mapXToTimeOffsetLocal, mapXOffsetToTimeOffsetLocal, convertXToTimeLocal } = useWaveformCoordinate(
  canvasState,
  canvasConfig,
  currentZoomLevel
);

// 使用可见时间范围计算 composable
const { visibleStartTime, visibleEndTime } = useVisibleTimeRange({
  virtualScrollOffset,
  currentZoomLevel,
  getLogicalGraphAreaWidth,
  getGraphAreaWidth,
  getEffectiveDuration,
  getAvailableParentWidth: () => {
    // 确保返回有效的父容器宽度
    const parentWidth = props.availableParentWidth;
    return parentWidth > 0 ? parentWidth : getGraphAreaWidth();
  },
  debugMode: false, // 关闭调试模式
});

// 拖拽处理器配置
const DRAG_THRESHOLD_MS = 100; // Threshold for long press to initiate drag

// 初始化拖拽处理器（需要在函数定义后配置）
let dragHandlerConfig: any;
let dragHandler: any;

// 自动滚动配置
const autoScrollConfig = {
  horizontalScrollbarRef,
  PADDING,
  logicalCanvasWidth,
  virtualScrollOffset,
  scrollLeftValue,
  getEffectiveDuration,
  getLogicalGraphAreaWidth,
  mapTimeToXLocal,
  containerRef: canvasContainer,
  graphContainerRef: graphContainer,
};

// 从 clickHandler 中获取所有需要的状态和方法
const {
  contextMenuEventId,
  isEventSelected,
  shouldIgnoreClick,
  setDragEndTime,
  handleCanvasClick: handleCanvasClickFromComposable,
  handleCanvasContextMenu: handleCanvasContextMenuFromComposable,
} = clickHandler;

// 使用事件绘制 composable（需要在 isEventSelected 定义后初始化）
const eventDrawingConfig = {
  mapTimeToXLocal,
  mapIntensityToYLocal,
  getGraphAreaWidth,
  isEventSelected,
  virtualScrollOffset,
  currentZoomLevel, // 传递当前缩放级别
  waveformStore,
};

const {
  drawTransientEvent,
  drawContinuousEvent,
  drawTransientEventSimplified,
  drawContinuousEventSimplified,
  invalidateEventCache,
  clearAllCache,
  resetEventDrawingState,
} = useWaveformEventDrawing(eventDrawingConfig);

// 使用缓存管理 Composable
const cacheConfig: WaveformCacheConfig = {
  waveformStore,
  getEvents: () => dataAccess.getEvents(), // 使用统一的数据访问接口
  getEffectiveDuration,
  scrollLeftValue,
  isDragging: ref(false), // 临时值，稍后会被拖拽处理器的值覆盖
  continuousEventCacheClear: clearAllCache,
};

const { shouldRedraw, resetDrawState } = useWaveformCache(cacheConfig);

// 使用辅助线管理 Composable
const guideConfig = {
  canvasHeight,
  padding: PADDING,
  mapYToIntensityLocal,
  getGraphAreaWidth,
};

// 创建一个包装函数来避免循环依赖
const drawWaveformWrapper = (forceRedraw?: boolean) => {
  // 这个函数会在 drawWaveform 定义后被调用
  if (typeof drawWaveform === "function") {
    drawWaveform(forceRedraw);
  } else if (typeof drawWaveformMain === "function") {
    drawWaveformMain(forceRedraw);
  }
};

const { hideRightClickGuide, showRightClickGuide, updateRightClickGuide, drawGuideLines } = useWaveformGuides(guideConfig, drawWaveformWrapper);

// 【修复】使用响应式的 computed 属性创建网格配置
// 这样当 props.availableParentWidth 变化时，配置会自动更新
const gridConfig = computed(() => ({
  getGraphAreaWidth,
  getGraphAreaHeight,
  getEffectiveDuration,
  getLogicalGraphAreaWidth,
  mapIntensityToYLocal,
  mapTimeToXLocal,
  currentZoomLevel, // 传递响应式缩放级别
  padding: PADDING,
  availableParentWidth: props.availableParentWidth,
}));

const { drawFullGrid, drawGrid } = useWaveformGrid(gridConfig);

// 根据显示模式获取振幅配置
const getAmplitudeConfig = () => {
  const mode = props.amplitudeDisplayMode || "standard";
  const preset = AMPLITUDE_PRESETS[mode];
  return {
    amplitudeScale: preset.amplitudeScale,
    amplitudeBoost: preset.amplitudeBoost,
  };
};

// 简化的 hasAudioData，仅检查 Store 中的音频数据
const hasAudioData = computed(() => {
  // 检查 Store 中的音频数据
  const storeAudioData = waveformStore.audioAmplitudeData;
  const hasStoreData = !!(storeAudioData && storeAudioData.samples && Array.isArray(storeAudioData.samples) && storeAudioData.samples.length > 0);

  return hasStoreData;
});

// 创建一个响应式的 showAudioWaveform 计算属性 - 结合 props 和音频数据状态
const showAudioWaveform = computed(() => {
  // 首先检查是否有音频数据
  const audioData = waveformStore.audioAmplitudeData;
  const hasAudioData = !!(audioData && audioData.samples && Array.isArray(audioData.samples) && audioData.samples.length > 0);

  console.log("InteractiveWaveformCanvas: showAudioWaveform 计算属性被调用");
  console.log("InteractiveWaveformCanvas: hasAudioData =", hasAudioData);
  console.log("InteractiveWaveformCanvas: props.showAudioWaveform =", props.showAudioWaveform);

  // 只有在有音频数据的情况下，才使用 props 中的显示控制
  const result = hasAudioData && props.showAudioWaveform;
  console.log("InteractiveWaveformCanvas: showAudioWaveform 计算结果 =", result);
  return result;
});

// 配置音频波形绘制功能
const audioWaveformConfig = {
  mapIntensityToYLocal,
  mapTimeToXLocal, // 添加时间到X坐标的映射函数，确保与时间刻度线坐标系统一
  getGraphAreaWidth,
  getGraphAreaHeight,
  getEffectiveDuration, // 添加有效时长获取函数，确保音频波形使用正确的时长基准
  virtualScrollOffset,
  currentZoomLevel,
  padding: PADDING, // 添加padding配置，确保与Event波形保持一致的布局
  enableSilenceFiltering: true,
  silenceThresholdPercent: 2,
  showWaveformBorder: false,
  amplitudeScale: 0.75,
  amplitudeBoost: 1.5,
};

// 使用音频波形 composable
const { drawAudioWaveform: drawAudioWaveformFunc, resetAudioWaveformState: clearAudioCache, setAudioData } = useAudioWaveform(audioWaveformConfig);

// 监听 store 中的音频数据变化，同步到 useAudioWaveform
watch(
  () => waveformStore.audioAmplitudeData,
  (newAudioData) => {
    setAudioData(newAudioData);

    // 输出音频数据信息到日志
    if (newAudioData && newAudioData.samples && Array.isArray(newAudioData.samples) && newAudioData.samples.length > 0) {
      const durationSeconds = (newAudioData.duration_ms / 1000).toFixed(2);
      const timeResolution = (newAudioData.duration_ms / newAudioData.samples.length).toFixed(2);
      const maxAmplitude = newAudioData.max_amplitude?.toFixed(4) || 'N/A';
      const minAmplitude = newAudioData.min_amplitude?.toFixed(4) || 'N/A';

      logger.info(LogModule.WAVEFORM, "🎵 音频数据已加载到 InteractiveWaveformCanvas", {
        fileUuid: validatedFileUuid,
        samplesCount: newAudioData.samples.length,
        durationMs: newAudioData.duration_ms,
        durationSeconds: `${durationSeconds}s`,
        sampleRate: newAudioData.sample_rate,
        timeResolution: `${timeResolution}ms/点`,
        maxAmplitude,
        minAmplitude,
        amplitudeRange: `${minAmplitude} ~ ${maxAmplitude}`,
        dataSize: `${(newAudioData.samples.length * 4 / 1024).toFixed(1)}KB`, // 假设每个样本4字节
      });
    } else if (newAudioData === null) {
      logger.debug(LogModule.WAVEFORM, "🎵 音频数据已清空", { fileUuid: validatedFileUuid });
    } else {
      logger.warn(LogModule.WAVEFORM, "🎵 接收到无效的音频数据", {
        fileUuid: validatedFileUuid,
        dataType: typeof newAudioData,
        hasData: !!newAudioData,
        hasSamples: !!(newAudioData?.samples),
        samplesLength: newAudioData?.samples?.length || 0
      });
    }
  },
  { immediate: true }
);

// 定义核心函数（需要在使用前定义）
const updateAndDrawCanvas = () => {
  // 如果菜单可见，先隐藏菜单
  if (isCanvasMenuVisible.value) {
    // 这里需要等待事件监听器初始化后才能调用
    // eventListeners.closeCanvasMenu();
  }

  if (!graphContainer.value) return;

  const currentAvailableWidth = props.availableParentWidth;
  const currentTotalDuration = props.totalEffectDuration;
  const currentBaselineDuration = props.baselineDuration;
  const currentAudioDuration = props.audioDuration;

  logger.debug(LogModule.WAVEFORM, "[WaveformGraph] 更新画布", {
    audioDuration: currentAudioDuration,
    totalDuration: currentTotalDuration,
  });

  // 计算逻辑宽度（用于坐标计算和滚动条）
  const targetLogicalWidth = calculateTargetGraphWidth(
    currentAvailableWidth,
    currentTotalDuration,
    currentBaselineDuration,
    PADDING.left,
    PADDING.right,
    currentAudioDuration
  );

  // 重置绘制状态缓存，确保重新计算
  resetDrawState();

  // 【修复】时长变化时重新计算缩放限制
  if (zoomManager && zoomManager.updateDynamicMinZoom) {
    // 更新缩放管理器的动态最小缩放限制
    zoomManager.updateDynamicMinZoom();
    logger.debug(LogModule.WAVEFORM, "[WaveformGraph] 时长变化，更新缩放限制", {
      newDuration: currentTotalDuration,
    });
  }

  // 当文件时长 ≤ FULL_SCREEN_TIME_MS 时，强制使用 1.0 缩放率
  if (currentTotalDuration <= FULL_SCREEN_TIME_MS && currentZoomLevel.value !== 1.0) {
    logger.debug(LogModule.WAVEFORM, "[WaveformGraph] 时长变化，调整缩放率为 1.0", {
      duration: currentTotalDuration,
      threshold: FULL_SCREEN_TIME_MS,
    });
    currentZoomLevel.value = 1.0;
    if (zoomManager && zoomManager.setZoomLevel) {
      zoomManager.setZoomLevel(1.0);
    }
  }

  // 设置画布容器的宽度（考虑当前缩放级别）
  if (canvasContainer.value) {
    // 使用当前缩放级别计算实际内容宽度
    const currentZoom = currentZoomLevel.value;
    const scaledLogicalWidth = targetLogicalWidth * currentZoom;
    const scaledContentWidth = scaledLogicalWidth - PADDING.left;
    canvasContainer.value.style.width = `${scaledContentWidth}px`;
  }

  // 使用逻辑宽度调整画布
  if (resizeCanvasLocal) {
    resizeCanvasLocal(targetLogicalWidth);
  }

  // 【修复】检查并修正滚动边界，确保时长变化后滚动状态正确
  if (horizontalScrollbarRef.value) {
    nextTick(() => {
      const wasCorrected = checkAndFixScrollBoundary(horizontalScrollbarRef.value);
      if (wasCorrected) {
        logger.debug(LogModule.WAVEFORM, "[WaveformGraph] 时长变化后修正了滚动边界");
      }
    });
  }

  // 强制重绘
  if (typeof smartDrawWaveform === "function") {
    smartDrawWaveform(true);
  } else if (typeof drawWaveform === "function") {
    drawWaveform(true);
  }
};

// 创建临时的频率调节键状态，稍后会被键盘处理器的实际值替换
const isFrequencyAdjustmentKeyPressed = ref<boolean>(false);

// 先初始化拖拽处理器以获取 isDragging
// 事件tooltip控制函数
const showEventTooltipFunc = (x: number, y: number) => {
  eventTooltipRef.value?.showEventTooltip(x, y);
};

const hideEventTooltipFunc = () => {
  eventTooltipRef.value?.hideEventTooltip();
};

const updateEventTooltipPositionFunc = (x: number, y: number) => {
  eventTooltipRef.value?.updateTooltipPosition(x, y);
};

// 现在初始化拖拽处理器，使用实际的 smartDrawWaveform
dragHandlerConfig = {
  canvas: waveformCanvas,
  events: () => dataAccess.getEvents(), // 使用统一的数据访问接口
  waveformStore,
  mapTimeToXLocal,
  mapIntensityToYLocal,
  mapYToIntensityLocal,
  mapXOffsetToTimeOffsetLocal,
  mapXToTimeOffsetLocal,
  smartDrawWaveform: () => {}, // 临时占位，稍后会被实际函数替换
  invalidateEventCache,
  isFrequencyAdjustmentKeyPressed,
  DRAG_THRESHOLD_MS,
  audioDuration: props.audioDuration,
  setDragEndTime,
  showEventTooltip: showEventTooltipFunc,
  hideEventTooltip: hideEventTooltipFunc,
  updateEventTooltipPosition: updateEventTooltipPositionFunc,
};

dragHandler = useDragHandler(dragHandlerConfig, autoScrollConfig);

// 从拖拽处理器中获取状态和方法
const {
  isDragging,
  draggedEvent,
  draggingTarget,
  draggedCurveIndex,
  longPressTimer,
  resetDragState: dragResetState,
  handleMouseDown: dragHandleMouseDown,
  handleMouseMove: dragHandleMouseMove,
  handleMouseUp: dragHandleMouseUp,
  currentDraggedFrequency,
  currentDraggedRelativeFrequency,
  currentDraggedTimeOffset,
  currentDraggedIntensity,
} = dragHandler;

// 先声明渲染管道变量，稍后初始化
let renderingPipeline: any;
let drawWaveform: (forceRedraw?: boolean) => void;
let smartDrawWaveform: (forceRedraw?: boolean) => void;

// 更新缓存配置中的 isDragging 值
cacheConfig.isDragging = isDragging;

// 事件tooltip组件引用和配置
const eventTooltipRef = ref<InstanceType<typeof EventTooltip> | null>(null);

// 事件tooltip配置
const eventTooltipConfig = {
  currentDraggedFrequency,
  currentDraggedRelativeFrequency,
  currentDraggedTimeOffset,
  currentDraggedIntensity,
  isDragging,
  draggingTarget,
  draggedEvent,
  draggedCurveIndex,
  isFrequencyAdjustmentKeyPressed,
};

// 重写虚拟滚动更新函数，使用 useWaveformCanvas
const updateVirtualScrollOffsetLocal = () => {
  const offsetChanged = updateVirtualScrollOffset();

  // 在边界位置时，强制重绘以确保X轴刻度同步
  if (offsetChanged) {
    asyncManager.createTimeout(() => {
      drawWaveform(true);
    }, 0);
  }

  // 更新Canvas位置以跟随虚拟滚动
  updateCanvasPosition(waveformCanvas.value);
};

// 使用绘制编排器
const drawingOrchestrator = useWaveformDrawingOrchestrator(
  {
    canvasCtx,
    waveformCanvas,
    canvasWidth,
    canvasHeight,
    waveformStore,
    drawTransientEvent,
    drawContinuousEvent,
    drawTransientEventSimplified,
    drawContinuousEventSimplified,
    drawFullGrid,
    drawGrid,
    drawAudioWaveform: drawAudioWaveformFunc, // 使用真实的音频波形绘制函数
    drawGuideLines,
    shouldRedraw,
    hasAudioData,
    isDragging,
    draggedEvent,
    // getVisibleEvents 已移除，直接在绘制编排器内部使用dataAccess.getEvents()
    getGraphAreaWidth,
    getGraphAreaHeight,
    // updatePerformanceStats 已移除
    createPreviewEvent: () => null, // 临时占位，稍后会被拖拽处理器的值替换
    PADDING,
  },
  {
    showAudioWaveform: showAudioWaveform,
  },
  () => dataAccess.getEvents() // 使用统一的数据访问接口
);

const { drawWaveformMain, drawWaveformLightweight } = drawingOrchestrator;

// 现在初始化渲染管道，使用实际的绘制函数
renderingPipeline = useWaveformRenderingPipeline({
  waveformStore,
  isDragging,
  asyncManager,
  drawWaveformMain,
  drawWaveformLightweight,
});

// 从渲染管道获取绘制函数
const renderingFunctions = renderingPipeline;
drawWaveform = renderingFunctions.drawWaveform;
smartDrawWaveform = renderingFunctions.smartDrawWaveform;

// 更新Canvas容器宽度以反映缩放后的实际内容宽度
const updateCanvasContainerWidth = (zoomLevel: number) => {
  if (!canvasContainer.value) return;

  // 【修复】如果 logicalCanvasWidth 还未正确设置，先计算正确的逻辑宽度
  let currentLogicalWidth = logicalCanvasWidth.value;
  if (currentLogicalWidth <= 0) {
    currentLogicalWidth = calculateTargetGraphWidth(
      props.availableParentWidth,
      props.totalEffectDuration,
      props.baselineDuration,
      PADDING.left,
      PADDING.right,
      props.audioDuration
    );
    logger.debug(LogModule.WAVEFORM, "[WaveformCanvas] logicalCanvasWidth 未设置，重新计算", {
      calculatedWidth: currentLogicalWidth,
      availableParentWidth: props.availableParentWidth,
      totalEffectDuration: props.totalEffectDuration,
    });
  }

  // 计算缩放后的实际内容宽度
  const scaledLogicalWidth = Math.round(currentLogicalWidth * zoomLevel);
  const scaledContentWidth = Math.round(scaledLogicalWidth - PADDING.left);
  canvasContainer.value.style.width = `${scaledContentWidth}px`;

  logger.debug(LogModule.WAVEFORM, "[WaveformCanvas] 更新容器宽度", {
    logicalWidth: currentLogicalWidth,
    zoomLevel,
    scaledLogicalWidth,
    scaledContentWidth,
  });
};

// 现在初始化缩放管理器，使用实际的绘制函数
const zoomConfig = {
  updateUIState: (updates: { zoomLevel: number }) => {
    // 集成缩放率持久化功能
    logger.debug(LogModule.WAVEFORM, "[WaveformZoom] 缩放级别更新", { zoomLevel: updates.zoomLevel });

    // 同步更新本地缩放级别状态
    currentZoomLevel.value = updates.zoomLevel;

    // 异步保存缩放率到 project.json
    zoomPersistence.saveZoomLevel(updates.zoomLevel).catch((error) => {
      logger.warn(LogModule.WAVEFORM, "[WaveformZoom] 保存缩放率失败", error);
    });
  },
  redrawWaveform: () => {
    // 在重绘前更新容器宽度以确保滚动条同步
    updateCanvasContainerWidth(currentZoomLevel.value);

    // 检查并修正滚动边界
    if (horizontalScrollbarRef.value) {
      checkAndFixScrollBoundary(horizontalScrollbarRef.value);
    }

    if (typeof smartDrawWaveform === "function") {
      smartDrawWaveform(true);
    } else if (typeof drawWaveform === "function") {
      drawWaveform(true);
    }
  },
  getEffectiveDuration: () => getEffectiveDuration(), // 传递时长获取函数

  // 动态最小缩放率计算所需参数
  getAvailableParentWidth: () => props.availableParentWidth,
  getBaselineDuration: () => props.baselineDuration,
  getPaddingLeft: () => PADDING.left,
  getPaddingRight: () => PADDING.right,
  getAudioDuration: () => props.audioDuration || null,

  debugMode: false, // 关闭调试模式
};

zoomManager = useWaveformZoom(zoomConfig);

// 【修复】先初始化画布尺寸，确保 logicalCanvasWidth 被正确设置
// 这样可以避免缩放恢复逻辑中容器宽度计算错误的问题
nextTick(() => {
  // 先执行一次画布更新，确保 logicalCanvasWidth 被正确设置
  updateAndDrawCanvas();

  // 然后恢复保存的缩放率，但需要考虑 FULL_SCREEN_TIME_MS 限制
  const restoreResult = zoomPersistence.restoreZoomLevel();
  const currentDuration = getEffectiveDuration(); // 获取当前文件时长

  if (currentDuration <= FULL_SCREEN_TIME_MS) {
    // 对于小于等于阈值的文件，强制使用 1.0 缩放率，确保内容铺满父元素宽度
    currentZoomLevel.value = 1.0;
    if (zoomManager.restoreZoomLevel) {
      zoomManager.restoreZoomLevel(1.0);
    }
    logger.debug(LogModule.WAVEFORM, "[WaveformZoom] 文件时长较短，强制使用缩放率 1.0", {
      duration: currentDuration,
      threshold: FULL_SCREEN_TIME_MS,
    });

    // 更新容器宽度和重绘
    nextTick(() => {
      updateCanvasContainerWidth(1.0);

      // 延迟重绘，确保容器宽度更新完成
      setTimeout(() => {
        if (typeof smartDrawWaveform === "function") {
          smartDrawWaveform(true);
        } else if (typeof drawWaveform === "function") {
          drawWaveform(true);
        }
      }, 50);
    });
  } else if (restoreResult.success && restoreResult.zoomLevel) {
    // 对于大于阈值的文件，正常恢复保存的缩放率
    currentZoomLevel.value = restoreResult.zoomLevel;
    if (zoomManager.restoreZoomLevel) {
      zoomManager.restoreZoomLevel(restoreResult.zoomLevel);
    }
    logger.debug(LogModule.WAVEFORM, "[WaveformZoom] 恢复缩放率", { zoomLevel: restoreResult.zoomLevel });

    // 恢复缩放率后需要立即更新容器宽度和重绘
    nextTick(() => {
      updateCanvasContainerWidth(restoreResult.zoomLevel!);

      // 延迟重绘，确保容器宽度更新完成
      setTimeout(() => {
        if (typeof smartDrawWaveform === "function") {
          smartDrawWaveform(true);
        } else if (typeof drawWaveform === "function") {
          drawWaveform(true);
        }
      }, 50);
    });
  } else {
    // 没有保存的缩放率，使用默认值
    currentZoomLevel.value = zoomManager.DEFAULT_ZOOM;
    logger.debug(LogModule.WAVEFORM, "[WaveformZoom] 使用默认缩放率", { zoomLevel: zoomManager.DEFAULT_ZOOM });
  }
});

// 延迟初始化动态最小缩放率，确保所有组件都已准备好
nextTick(() => {
  if (zoomManager.initializeDynamicMinZoom) {
    zoomManager.initializeDynamicMinZoom();
  }
});

// 监听缩放级别变化，确保滚动边界正确更新
watch(
  currentZoomLevel,
  (newZoomLevel, oldZoomLevel) => {
    // 避免重复处理相同的缩放级别
    if (Math.abs(newZoomLevel - (oldZoomLevel || 0)) < 0.001) {
      return;
    }

    logger.debug(LogModule.WAVEFORM, "[WaveformCanvas] 缩放级别变化", {
      newZoomLevel,
      oldZoomLevel,
    });

    // 更新Canvas容器宽度以反映缩放后的实际内容宽度
    nextTick(() => {
      updateCanvasContainerWidth(newZoomLevel);

      // 检查并修正滚动边界，确保缩放后不会出现空白区域
      if (horizontalScrollbarRef.value) {
        const wasCorrected = checkAndFixScrollBoundary(horizontalScrollbarRef.value);
        if (wasCorrected) {
          logger.debug(LogModule.WAVEFORM, "[WaveformCanvas] 缩放后修正了滚动边界");
        }
      }

      // 延迟重绘，确保X轴显示范围正确更新
      setTimeout(() => {
        if (typeof smartDrawWaveform === "function") {
          smartDrawWaveform(true);
        } else if (typeof drawWaveform === "function") {
          drawWaveform(true);
        }
      }, 20);
    });
  },
  { immediate: false }
);

// 更新拖拽处理器配置中的 smartDrawWaveform
dragHandlerConfig.smartDrawWaveform = smartDrawWaveform;

// 更新绘制编排器配置中的 createPreviewEvent
// 这需要重新创建绘制编排器，因为配置是不可变的
// 但为了简化，我们可以直接在绘制编排器内部处理这个问题

// 现在初始化右键菜单 composable
const contextMenuConfig: ContextMenuConfig = {
  waveformStore,
  getEffectiveDuration,
  audioDuration: props.audioDuration,
  showRightClickGuide,
  updateRightClickGuide,
  hideRightClickGuide,
  resetDrawState,
  throttledDrawWaveform: drawWaveform, // 实验4: 使用基础绘制函数替代节流版本
  handleCanvasContextMenuFromComposable: handleCanvasContextMenuFromComposable,
  dataAccess, // 添加数据访问接口用于调试
};

const {
  isCanvasMenuVisible,
  canvasMenuPosition,
  canvasMenuTimePosition,
  canAddTransient,
  canAddContinuous,
  availableSpaceText,
  MIN_TRANSIENT_DURATION,
  MIN_CONTINUOUS_DURATION,
  handleCanvasContextMenu: handleCanvasContextMenuFromContextMenu,
  handleAddTransientEvent: handleAddTransientEventFromContextMenu,
  handleAddContinuousEvent: handleAddContinuousEventFromContextMenu,
  handleDeleteEvent: handleDeleteEventFromContextMenu,
  closeCanvasMenu: closeCanvasMenuFromContextMenu,
  handleMenuMouseEnter: handleMenuMouseEnterFromContextMenu,
  handleMenuMouseLeave: handleMenuMouseLeaveFromContextMenu,
  handleDocumentClick: handleDocumentClickFromContextMenu,
  cleanup: contextMenuCleanup,
} = useWaveformContextMenu(contextMenuConfig);

// 现在初始化键盘处理器，使用实际的绘制函数
const keyboardConfig: KeyboardConfig = {
  waveformStore,
  waveformCanvas,
  draggingTarget,
  draggedEvent,
  draggedCurveIndex,
  isCanvasMenuVisible,
  closeCanvasMenuFromContextMenu,
  resetDrawState,
  throttledDrawWaveform: drawWaveform, // 使用实际的绘制函数
  hideEventTooltip: hideEventTooltipFunc,
  showEventTooltip: showEventTooltipFunc,
};

// 使用键盘事件处理 composable
const { isFrequencyAdjustmentKeyPressed: keyboardFrequencyKey, handleKeyDown, handleKeyUp } = useWaveformKeyboard(keyboardConfig);

// 同步频率调节键状态
watch(
  keyboardFrequencyKey,
  (newValue) => {
    isFrequencyAdjustmentKeyPressed.value = newValue;
  },
  { immediate: true }
);

// 确保历史记录系统已初始化
if (waveformStore.getHistorySystem) {
  const historySystem = waveformStore.getHistorySystem();
  logger.debug(LogModule.WAVEFORM, "历史记录系统已初始化", {
    canUndo: historySystem?.canUndo?.() || false,
    canRedo: historySystem?.canRedo?.() || false,
  });
}

// 配置事件监听器管理 composable
const eventListenerConfig: EventListenerConfig = {
  // 基础引用
  waveformCanvas,
  canvasCtx,
  horizontalScrollbarRef,
  longPressTimer,

  // Store 和状态
  waveformStore,
  props: {
    ...props,
    // events已移除，现在完全使用dataAccess
    onEventSelected: (id: string | null) => emit("event-selected", id),
  },

  // 数据访问接口（新增）
  dataAccess,

  // 坐标转换函数
  mapTimeToXLocal,
  mapIntensityToYLocal,
  mapYToIntensityLocal,
  mapXToTimeOffsetLocal,
  convertXToTimeLocal,

  // 画布状态函数
  getEffectiveDuration,
  getGraphAreaWidth,
  getLogicalGraphAreaWidth,

  // 绘制函数
  smartDrawWaveform,
  throttledDrawWaveform: drawWaveform, // 实验4: 使用基础绘制函数
  resetDrawState,

  // 拖拽相关
  isDragging,
  draggedEvent,
  draggingTarget,
  draggedCurveIndex,

  // 拖拽处理函数
  dragHandleMouseDown,
  dragHandleMouseUp,
  dragHandleMouseMove,
  dragResetState,

  // 点击处理
  handleCanvasClickFromComposable,
  shouldIgnoreClick,
  setDragEndTime,

  // 右键菜单相关
  isCanvasMenuVisible,
  closeCanvasMenuFromContextMenu,
  handleCanvasContextMenuFromComposable,
  handleCanvasContextMenuFromContextMenu,
  handleAddTransientEventFromContextMenu,
  handleAddContinuousEventFromContextMenu,
  handleDeleteEventFromContextMenu,
  handleMenuMouseEnterFromContextMenu,
  handleMenuMouseLeaveFromContextMenu,
  handleDocumentClickFromContextMenu,
  contextMenuEventId,

  // 辅助线相关
  hideRightClickGuide,

  // 缩放相关
  handleWheelZoom: (event: WheelEvent, mouseX: number, mouseY: number) => {
    zoomManager.handleWheelZoom(event, mouseX, mouseY);
    // 立即同步缩放级别，避免状态不一致
    const newZoomLevel = zoomManager.currentZoom.value;
    if (Math.abs(currentZoomLevel.value - newZoomLevel) > 0.001) {
      currentZoomLevel.value = newZoomLevel;
    }
  },
  currentZoomLevel, // 传递当前缩放级别

  // 虚拟滚动相关
  updateVirtualScrollOffset,
  updateVirtualScrollOffsetLocal,
  checkAndFixScrollBoundary,
  scrollLeftValue,
  virtualScrollOffset,
  logicalCanvasWidth,
  canvasWidth,
  canvasHeight,

  // 常量
  PADDING,
};

const eventListeners = useWaveformEventListeners(eventListenerConfig);

const resizeCanvasLocal = (targetWidth: number) => {
  if (!graphContainer.value || !waveformCanvas.value || !canvasCtx.value) return;

  resizeCanvas(targetWidth, waveformCanvas.value, canvasCtx.value, graphContainer.value);

  // 通知Y轴组件更新
  if (yAxisAreaRef.value) {
    yAxisAreaRef.value.redraw();
  }

  // 强制重绘
  if (typeof drawWaveform === "function") {
    drawWaveform();
  }
};

// 监听events数据变化，确保历史记录系统在数据加载后正确初始化
watch(
  () => dataAccess.getEvents(),
  (newEvents, oldEvents) => {
    // 只在从空数组变为非空数组时触发历史记录初始化
    // 这样可以避免在用户操作时错误地重新初始化历史记录
    if ((!oldEvents || oldEvents.length === 0) && newEvents && newEvents.length > 0) {
      logger.debug(LogModule.WAVEFORM, "检测到events从空变为非空，确保历史记录系统正确初始化", {
        oldEventsCount: oldEvents?.length || 0,
        newEventsCount: newEvents.length,
      });

      // 确保历史记录系统使用正确的初始状态
      if (waveformStore.ensureHistoryInitialized) {
        waveformStore.ensureHistoryInitialized();
      }
    }
  },
  { immediate: false, deep: false }
);

// 使用监听器管理器（已移除events参数，直接使用store中的数据）
const watchersManager = useWaveformWatchers({
  waveformStore,
  projectStore,
  audioAmplitudeData: undefined, // 不再从 props 传递，直接从 Store 获取
  showAudioWaveform: showAudioWaveform, // 使用计算属性，基于音频数据自动判断
  isUsingCachedData: toRef(props, "isUsingCachedData"),
  amplitudeDisplayMode: props.amplitudeDisplayMode,
  totalEffectDuration: props.totalEffectDuration,
  baselineDuration: props.baselineDuration,
  availableParentWidth: props.availableParentWidth,
  audioDuration: props.audioDuration,
  hasAudioData,
  updateAndDrawCanvas,
  setAudioData: () => {}, // 空实现：音频数据现在由只读组件管理
  clearAudioData: () => {}, // 空实现：音频数据现在由只读组件管理
  getAmplitudeConfig,
  asyncManager,
});

// 单独监听音频波形显示状态变化，确保立即重绘
watch(
  showAudioWaveform, // 监听计算属性，基于音频数据自动判断
  () => {
    nextTick(() => {
      drawingOrchestrator.drawWaveformMain(true); // 强制重绘
    });
  }
);

// 配置生命周期管理 composable
const lifecycleRefs: LifecycleRefs = {
  graphContainer,
  canvasContainer,
  waveformCanvas,
  canvasCtx,
  yAxisAreaRef,
  horizontalScrollbarRef,
  longPressTimer,
};

const lifecycleHandlers: LifecycleEventHandlers = {
  handleCanvasClick: eventListeners.handleCanvasClick,
  handleCanvasContextMenu: eventListeners.handleCanvasContextMenu,
  handleMouseLeave: eventListeners.handleMouseLeave,
  handleMouseHover: eventListeners.handleMouseHover,
  handleMouseDown: eventListeners.handleMouseDown,
  handleMouseMove: eventListeners.handleMouseMove,
  handleMouseUp: eventListeners.handleMouseUp,
  dragHandleMouseDown,
  dragHandleMouseUp,
  dragHandleMouseMove,
  handleKeyDown,
  handleKeyUp,
  handleGraphWheelScroll: eventListeners.handleGraphWheelScroll,
  handleDocumentClick: eventListeners.handleDocumentClick,
};

const lifecycleConfig: LifecycleConfig = {
  updateAndDrawCanvas,
  updateCanvasPosition,
  contextMenuCleanup,
  resizeCanvasLocal, // 传递 Canvas 尺寸调整函数
};

// 使用生命周期管理 composable（自动处理生命周期）
const lifecycleManager = useWaveformLifecycle(lifecycleRefs, lifecycleHandlers, lifecycleConfig);

// 监听可见时间范围变化并发射事件
watch(
  [visibleStartTime, visibleEndTime],
  ([newStartTime, newEndTime]) => {
    emit("visible-time-range-changed", newStartTime, newEndTime);
  },
  { immediate: true }
);

// 额外监听虚拟滚动偏移和缩放级别变化，确保时间范围变化能被触发
watch(
  [virtualScrollOffset, currentZoomLevel],
  () => {
    // 强制触发时间范围重新计算
    nextTick(() => {
      const currentStartTime = visibleStartTime.value;
      const currentEndTime = visibleEndTime.value;
      emit("visible-time-range-changed", currentStartTime, currentEndTime);
    });
  },
  { immediate: false }
);

// 使用清理管理器
const cleanupManager = useWaveformCleanup({
  stopAllWatchers: watchersManager.stopAllWatchers,
  asyncManager,
  clearAudioData: clearAudioCache, // 使用真实的音频缓存清理函数
  resetAudioWaveformState: clearAudioCache, // 使用真实的音频波形状态清理函数
  clearAllCache,
  resetEventDrawingState,
  contextMenuCleanup,
  resetDrawState,
  dragResetState,
  // resetPerformanceState 已移除
  lifecycleManager,
});

// 组件卸载时执行清理
onBeforeUnmount(() => {
  logger.debug(LogModule.WAVEFORM, "WaveformGraph: 组件即将卸载，开始清理资源");
  cleanupManager.performCompleteCleanup();
});

// 编程式数据更新API接口定义
export interface WaveformProgrammaticAPI {
  // 完整数据替换
  setEventsData(events: RenderableEvent[], options?: import("@/stores/haptics-editor-store").BatchUpdateOptions): Promise<void>;

  // 增量数据更新
  updateEventsData(updates: Array<{ id: string; data: Partial<RenderableEvent> }>, options?: import("@/stores/haptics-editor-store").BatchUpdateOptions): Promise<void>;

  // 范围替换
  replaceEventsInRange(
    startTime: number,
    endTime: number,
    newEvents: RenderableEvent[],
    options?: import("@/stores/haptics-editor-store").BatchUpdateOptions
  ): Promise<void>;

  // 获取当前数据状态
  getCurrentEventsData(): RenderableEvent[];

  // 强制重绘
  forceRedraw(): Promise<void>;

  // 数据验证
  validateEventsData(events: RenderableEvent[]): import("@/stores/haptics-editor-store").ValidationResult;
}

// 编程式API实现
const programmaticAPI: WaveformProgrammaticAPI = {
  async setEventsData(events: RenderableEvent[], options: import("@/stores/haptics-editor-store").BatchUpdateOptions = {}) {
    try {
      // 调用store的批量更新方法
      waveformStore.setBatchEvents(events, options);

      // 等待下一个tick确保DOM更新完成
      await nextTick();

      // 如果需要强制重绘
      if (options.forceRedraw !== false) {
        await new Promise<void>((resolve) => {
          asyncManager.createTimeout(
            () => {
              updateAndDrawCanvas();
              resolve();
            },
            0,
            "programmatic-update-redraw"
          );
        });
      }
    } catch (error) {
      logger.error(LogModule.WAVEFORM, "Failed to set events data", error);
      throw error;
    }
  },

  async updateEventsData(updates: Array<{ id: string; data: Partial<RenderableEvent> }>, options: import("@/stores/haptics-editor-store").BatchUpdateOptions = {}) {
    try {
      waveformStore.updateBatchEvents(updates, options);
      await nextTick();

      if (options.forceRedraw !== false) {
        await new Promise<void>((resolve) => {
          asyncManager.createTimeout(
            () => {
              updateAndDrawCanvas();
              resolve();
            },
            0,
            "programmatic-update-redraw"
          );
        });
      }
    } catch (error) {
      logger.error(LogModule.WAVEFORM, "Failed to update events data", error);
      throw error;
    }
  },

  async replaceEventsInRange(startTime: number, endTime: number, newEvents: RenderableEvent[], options: import("@/stores/haptics-editor-store").BatchUpdateOptions = {}) {
    try {
      waveformStore.replaceEventsInRange(startTime, endTime, newEvents, options);
      await nextTick();

      if (options.forceRedraw !== false) {
        await new Promise<void>((resolve) => {
          asyncManager.createTimeout(
            () => {
              updateAndDrawCanvas();
              resolve();
            },
            0,
            "programmatic-update-redraw"
          );
        });
      }
    } catch (error) {
      logger.error(LogModule.WAVEFORM, "Failed to replace events in range", error);
      throw error;
    }
  },

  getCurrentEventsData(): RenderableEvent[] {
    return dataAccess.getEvents();
  },

  async forceRedraw(): Promise<void> {
    return new Promise<void>((resolve) => {
      asyncManager.createTimeout(
        () => {
          updateAndDrawCanvas();
          resolve();
        },
        0,
        "force-redraw"
      );
    });
  },

  validateEventsData(events: RenderableEvent[]): import("@/stores/haptics-editor-store").ValidationResult {
    return waveformStore.validateEventsData(events);
  },
};

// 暴露编程式API
defineExpose(programmaticAPI);
</script>

<style scoped>
/* ===== 波形图主容器，继承父级卡片背景 ===== */
.waveform-graph {
  position: relative;
  height: 100%;
  display: flex;
  width: 100%;
  overflow: hidden;
}

/* 画布容器，用于虚拟滚动 */
.canvas-container {
  position: relative;
  height: 100%;
  /* 宽度由 JavaScript 动态设置为逻辑宽度 */
}

.waveform-canvas {
  display: block;
  position: absolute;
  left: 0;
  top: 0;
}

/* 滚动条样式优化 - 深色主题下更容易看清 */
:deep(.waveform-scrollbar) {
  /* 滚动条轨道背景 */
  --n-scrollbar-track-color: rgba(240, 232, 232, 0.7) !important;
  /* 滚动条滑块颜色 */
  --n-scrollbar-color: rgba(236, 231, 231, 0.6) !important;
  /* 滚动条滑块悬停颜色 */
  --n-scrollbar-color-hover: rgba(236, 231, 231, 0.5) !important;
  /* 滚动条边框颜色 */
  --n-scrollbar-border-color: rgba(236, 231, 231, 0.2) !important;
}
</style>
