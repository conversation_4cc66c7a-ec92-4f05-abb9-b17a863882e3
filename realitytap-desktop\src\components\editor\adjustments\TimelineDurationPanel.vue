<template>
  <div class="duration-adjust-panel">
    <!-- 当有音频时，显示音频波形控制按钮 -->
    <div v-if="hasAudio && !isDisabled" class="controls-container">
      <div class="audio-waveform-controls">
        <!-- 显示音频按钮 -->
        <n-button
          v-if="!showAudioWaveform"
          type="default"
          @click="handleShowAudio"
          class="audio-control-btn"
          secondary
        >
          <template #icon>
            <n-icon class="button-icon"><VolumeHighIcon /></n-icon>
          </template>
          <span class="button-text">{{ t('editor.durationPanel.showAudio') }}</span>
        </n-button>

        <!-- 隐藏音频按钮 -->
        <n-button
          v-if="showAudioWaveform"
          type="default"
          @click="handleHideAudio"
          class="audio-control-btn"
          secondary
        >
          <template #icon>
            <n-icon class="button-icon"><VolumeOffIcon /></n-icon>
          </template>
          <span class="button-text">{{ t('editor.durationPanel.hideAudio') }}</span>
        </n-button>
      </div>
    </div>

    <!-- 当没有音频时，显示增加时长控制 -->
    <div v-else-if="!isDisabled" class="controls-container">
      <div class="duration-controls">
        <n-popover
          trigger="click"
          placement="top"
          :show="showDurationInput"
          @update:show="showDurationInput = $event"
          :width="180"
          class="duration-popover"
          :overlap="true"
          :theme-overrides="popoverTheme"
        >
          <template #trigger>
            <n-button
              type="primary"
              @click="handleShowPopover"
              class="add-duration-btn"
              secondary
              :disabled="!isTimelineAdjustable"
            >
              <template #icon>
                <n-icon class="button-icon"><PlusCircleIcon /></n-icon>
              </template>
              <span class="button-text">{{ t('editor.durationPanel.increaseDuration') }}</span>
            </n-button>
          </template>

          <!-- Popover 内容 -->
          <div class="duration-input-group">
            <div class="input-wrapper">
              <n-input-number
                v-model:value="durationForm.value"
                :min="1"
                :precision="0"
                size="small"
                ref="inputRef"
                class="duration-input"
                :default-value="100"
                :show-button="false"
              />
              <span class="unit-label">{{ t('editor.durationPanel.milliseconds') }}</span>
            </div>
            <div class="button-wrapper">
              <n-button type="primary" size="small" @click="handleConfirm" class="confirm-btn">
                <template #icon>
                  <n-icon><CheckIcon /></n-icon>
                </template>
                {{ t('editor.durationPanel.confirm') }}
              </n-button>
            </div>
          </div>
        </n-popover>
      </div>
    </div>
    <div v-else class="duration-disabled-message">
      <!-- 音频切换按钮已移除：现在根据音频数据自动显示 -->
      <!-- 音频检测信息显示已移除 -->
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick, watch, computed, defineEmits } from "vue";
import { NButton, NInputNumber, NIcon, NPopover, useMessage } from "naive-ui";
import {
  AddCircleOutline as PlusCircleIcon,
  CheckmarkOutline as CheckIcon,
  VolumeHighOutline as VolumeHighIcon,
  VolumeOffOutline as VolumeOffIcon,
} from "@vicons/ionicons5";
import { MAX_HAPTIC_DURATION_MS } from "../waveform/config/waveform-constants";
import { useI18n } from "@/composables/useI18n";
// 移除了音频切换相关的 store 导入

const props = defineProps<{
  audioDuration?: number | null;
  isTimelineAdjustable?: boolean;
  currentTotalDuration?: number; // 当前总时长，用于边界检查
  fileUuid?: string; // 文件UUID，用于音频数据重新加载
}>();

const emit = defineEmits(["increase-duration", "toggle-audio-waveform"]);

const message = useMessage();
const { t } = useI18n();

const showDurationInput = ref(false);
const inputRef = ref(null);

const durationForm = ref({
  value: 100,
});

const isDisabled = computed(() => {
  return (props.audioDuration != null && props.audioDuration > 0) || !props.isTimelineAdjustable;
});

// 音频波形显示状态管理
const showAudioWaveform = ref(false); // 默认不显示音频波形

// 检查是否有音频数据
const hasAudio = computed(() => {
  return props.audioDuration != null && props.audioDuration > 0;
});

const handleShowPopover = () => {
  showDurationInput.value = true;
};

const handleConfirm = () => {
  // 确保所有时间和时长都是整数
  const durationToAdd = Math.floor(durationForm.value.value || 0);
  const currentDuration = Math.floor(props.currentTotalDuration || 0);
  const newTotalDuration = currentDuration + durationToAdd;

  // 检查是否超过最大时长限制
  if (newTotalDuration > MAX_HAPTIC_DURATION_MS) {
    // 计算实际可以增加的时长（确保为整数）
    const maxAllowedIncrease = Math.floor(MAX_HAPTIC_DURATION_MS - currentDuration);

    if (maxAllowedIncrease <= 0) {
      // 已经达到最大时长，无法再增加
      message.warning(t('editor.durationPanel.maxDurationReached', { max: MAX_HAPTIC_DURATION_MS }));
    } else {
      // 裁剪到最大允许值并提示用户
      message.info(t('editor.durationPanel.durationLimitedToMax', {
        max: MAX_HAPTIC_DURATION_MS,
        planned: durationToAdd,
        actual: maxAllowedIncrease
      }));
      emit("increase-duration", maxAllowedIncrease);
    }
  } else {
    // 正常增加时长
    emit("increase-duration", durationToAdd);
  }

  // Reset form to default value
  durationForm.value.value = 100;
  showDurationInput.value = false;
};

// 音频波形控制事件处理器
const handleShowAudio = () => {
  showAudioWaveform.value = true;
  emit("toggle-audio-waveform", true);
};

const handleHideAudio = () => {
  showAudioWaveform.value = false;
  emit("toggle-audio-waveform", false);
};



watch(showDurationInput, (newVal: boolean) => {
  if (newVal && !isDisabled.value && inputRef.value) {
    nextTick(() => {
      // @ts-ignore
      inputRef.value?.focus();
    });
  }
});

watch(isDisabled, (newIsDisabled) => {
  if (newIsDisabled && showDurationInput.value) {
    showDurationInput.value = false;
  }
});

// 音频显示状态监听已移除：现在根据音频数据自动显示

const popoverTheme = {
  color: "#242830",
  borderColor: "#3a4255",
  textColor: "#e0e0e0",
  boxShadow: "0 3px 12px rgba(0, 0, 0, 0.2)",
  padding: "12px",
};
</script>

<style scoped>
.duration-adjust-panel {
  height: 100%;
  width: 180px;
  min-width: 180px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.controls-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.duration-controls {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.audio-waveform-controls {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.add-duration-btn {
  width: 95%;
  height: 36px;
  background-color: transparent !important;
  border-radius: 18px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 12px;
}

.button-icon {
  font-size: 18px;
  margin-right: 8px;
  color: #e0e0e0 !important;
}

.button-text {
  font-size: 14px;
  font-weight: 500;
  color: #e0e0e0 !important;
}

.add-duration-btn:hover {
  background-color: #333b4d !important;
  border-color: #4e5a73 !important;
}

.audio-control-btn {
  width: 95%;
  height: 36px;
  background-color: transparent !important;
  border-radius: 18px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 12px;
}

.audio-control-btn:hover {
  background-color: #333b4d !important;
  border-color: #4e5a73 !important;
}

.duration-input-group {
  display: flex;
  flex-direction: column;
  justify-content: center;
  width: 100%;
  align-items: center;
}

.input-wrapper {
  display: flex;
  gap: 10px;
  align-items: center;
  width: 100%;
  box-sizing: border-box;
  margin-bottom: 10px;
}

.duration-input {
  flex: 1;
}

.unit-label {
  color: #e0e0e0;
  font-size: 13px;
  padding-right: 5px;
}

.button-wrapper {
  display: flex;
  width: 100%;
  box-sizing: border-box;
  justify-content: center;
}

.confirm-btn {
  width: 100%;
  color: #ffffff;
  background-color: #2b3c4e !important;
  border: 1px solid #395167 !important;
  transition: all 0.2s ease;
}

.confirm-btn:hover {
  color: #ffffff;
  background-color: #34495e !important;
  border-color: #476582 !important;
}

.cancel-btn {
  flex: 1;
  border: 1px solid #4c5366 !important;
  color: #c0c0c0 !important;
  transition: all 0.2s ease;
}

.cancel-btn:hover {
  border-color: #5e6680 !important;
  background-color: rgba(94, 102, 128, 0.1) !important;
}

:deep(.n-input-number .n-input) {
  background-color: #2b3241 !important;
  border: 1px solid #3a4255 !important;
  border-radius: 3px !important;
}

:deep(.n-input-number .n-input:hover) {
  border-color: #36ad6a !important;
}

:deep(.n-input-number .n-input.n-input--focus),
:deep(.n-input-number .n-input:focus-within) {
  border-color: #4e5a73 !important;
}

:deep(.n-input-number .n-input__input-el) {
  background-color: transparent !important;
  color: #e0e0e0 !important;
}

:deep(.n-input-number .n-input__border),
:deep(.n-input-number .n-input__state-border) {
  background-color: transparent !important;
  border-color: #3a4255 !important;
}

:deep(.n-input-number .n-input:hover .n-input__border),
:deep(.n-input-number .n-input:hover .n-input__state-border) {
  border-color: #36ad6a !important;
}

:deep(.n-input-number .n-input__input-el::placeholder) {
  color: #859099 !important;
}
:deep(.n-input-number .n-input__placeholder) {
  color: #859099 !important;
}

.duration-disabled-message {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 4px 0px;
  box-sizing: border-box;
  color: #859099;
  font-size: 13px;
  text-align: center;
}

.audio-toggle-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
}

.audio-toggle-btn {
  width: 75%;
  height: 36px;
  background-color: transparent !important;
  border-radius: 18px;
  color: #e0e0e0 !important;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 12px;
}

.audio-toggle-btn:hover {
  background-color: #333b4d !important;
  border-color: #4e5a73 !important;
  color: #e0e0e0 !important;
}

.audio-toggle-btn[data-type="primary"] {
  background-color: #36ad6a !important;
  border-color: #36ad6a !important;
  color: #ffffff !important;
}

.audio-toggle-btn[data-type="primary"]:hover {
  background-color: #2d8f57 !important;
  border-color: #2d8f57 !important;
  color: #ffffff !important;
}

/* 确保按钮内的文字和图标颜色正确 */
.audio-toggle-btn .button-text {
  color: inherit !important;
}

.audio-toggle-btn .button-icon {
  color: inherit !important;
}

/* Popover 样式定制 */
:deep(.n-popover) {
  background-color: #242830 !important;
  border: 1px solid #3a4255 !important;
  box-shadow: 0 3px 12px rgba(0, 0, 0, 0.2) !important;
}

:deep(.n-popover-arrow) {
  border-color: #3a4255 !important;
  background-color: #242830 !important;
}

:deep(.n-popover-content) {
  color: #e0e0e0 !important;
  background-color: #242830 !important;
}

:deep(.n-popover__content) {
  background-color: #242830 !important;
  color: #e0e0e0 !important;
  padding: 12px !important;
}

:deep(.duration-popover .n-popover__content) {
  padding: 12px !important;
  background-color: #242830 !important;
}


</style>
